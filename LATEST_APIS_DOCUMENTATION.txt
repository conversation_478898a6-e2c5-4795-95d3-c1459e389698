LATEST APIS DOCUMENTATION
=========================

This document contains all the APIs generated in this session.

================================================================================
1. VISITOR TRACKING API
================================================================================

1.1 Track Visitor (Public)
---------------------------
URL: POST /api/website/track-visitor
Purpose: Records visitor data for promotional pages
Authentication: None required

Request Payload:
{
  "page_name": "home",
  "page_url": "http://localhost:3000/",
  "page_path": "/",
  "session_id": "session_1750650997915_koqgeipxe",
  "visitor_id": "visitor_1750650997915_4c2tq88p3",
  "browser_name": "Chrome",
  "browser_version": "*********",
  "user_agent": "Mozilla/5.0...",
  "device_type": "Desktop",
  "operating_system": "Linux",
  "screen_width": 1920,
  "screen_height": 1080,
  "viewport_width": 1864,
  "viewport_height": 547,
  "timezone": "Asia/Dhaka",
  "language": "en-US",
  "referrer": "Direct",
  "utm_source": null,
  "utm_medium": null,
  "utm_campaign": null,
  "utm_term": null,
  "utm_content": null,
  "visited_at": "2025-06-23T04:11:31.418Z",
  "page_type": "landing",
  "section": "promotional"
}

Response:
{
  "success": true,
  "message": "Visitor tracked successfully",
  "data": {
    "tracking_id": 4
  }
}

1.2 Get Visitor Statistics (Admin)
-----------------------------------
URL: GET /api/website/admin/visitor-stats?date_range=7
Purpose: Get aggregated visitor analytics
Authentication: Required

Query Parameters:
- date_range: Number of days to look back (default: 7)

Response:
{
  "success": true,
  "data": {
    "summary": {
      "total_visitors": 1250,
      "total_page_views": 3420,
      "date_range": 7
    },
    "page_stats": [
      {
        "page_name": "home",
        "views": 1500,
        "unique_visitors": 800
      }
    ],
    "device_stats": [
      {
        "device_type": "Desktop",
        "count": 2100
      }
    ],
    "browser_stats": [
      {
        "browser_name": "Chrome",
        "count": 1800
      }
    ],
    "daily_stats": [
      {
        "date": "2023-12-21",
        "page_views": 450,
        "unique_visitors": 320
      }
    ],
    "utm_stats": [
      {
        "utm_source": "google",
        "count": 800
      }
    ]
  }
}

1.3 Get Visitor List (Admin)
-----------------------------
URL: GET /api/website/admin/visitor-list?per_page=50&page=1
Purpose: Get detailed visitor records with pagination
Authentication: Required

Query Parameters:
- per_page: Records per page (default: 50)
- page: Page number (default: 1)
- page_name: Filter by page (optional)
- date_range: Days to look back (default: 7)

Response:
{
  "success": true,
  "data": {
    "visitors": [
      {
        "id": 123,
        "page_name": "home",
        "page_url": "https://yourdomain.com/",
        "visitor_id": "visitor_1703123456789_xyz789ghi",
        "session_id": "session_1703123456789_abc123def",
        "browser_name": "Chrome",
        "browser_version": "120.0.6099.109",
        "device_type": "Desktop",
        "operating_system": "Windows",
        "timezone": "America/New_York",
        "language": "en-US",
        "referrer": null,
        "utm_source": "google",
        "utm_medium": "cpc",
        "utm_campaign": "winter_sale",
        "visited_at": "2023-12-21T10:30:45.000000Z",
        "created_at": "2023-12-21T10:30:46.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 50,
      "total": 1250,
      "last_page": 25
    }
  }
}

================================================================================
2. CYBERSOURCE SECURE ACCEPTANCE API
================================================================================

2.1 Generate Secure Acceptance Payment
---------------------------------------
URL: POST /api/website/cybersource/secure-acceptance/generate
Purpose: Generate payment form for CyberSource hosted payment
Authentication: Required

Request Payload:
{
  "amount": "100.00",
  "currency": "USD",
  "reference_number": "ORDER-123456",
  "customer_email": "<EMAIL>",
  "customer_name": "John Doe",
  "customer_phone": "+1234567890",
  "billing_address": {
    "line1": "123 Main Street",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "US"
  }
}

Response:
{
  "success": true,
  "message": "Secure Acceptance payment form generated successfully",
  "data": {
    "form_fields": {
      "access_key": "ad36521de6533073afc8626952c910b1",
      "profile_id": "mehedi54321_1750580586",
      "transaction_uuid": "uuid-generated-value",
      "signed_field_names": "access_key,profile_id,transaction_uuid...",
      "unsigned_field_names": "",
      "signed_date_time": "2023-12-21T10:30:45Z",
      "locale": "en",
      "transaction_type": "sale",
      "reference_number": "ORDER-123456",
      "amount": "100.00",
      "currency": "USD",
      "customer_email": "<EMAIL>",
      "bill_to_forename": "John",
      "bill_to_surname": "Doe",
      "bill_to_email": "<EMAIL>",
      "signature": "generated-signature-value"
    },
    "action_url": "https://testsecureacceptance.cybersource.com/pay",
    "method": "POST",
    "transaction_uuid": "uuid-generated-value"
  }
}

2.2 Payment Success Callback
-----------------------------
URL: POST /payment/success
Purpose: Handle successful payment callback from CyberSource
Authentication: None (callback from CyberSource)

This endpoint receives POST data from CyberSource and redirects to frontend.

2.3 Payment Cancel Callback
----------------------------
URL: POST /payment/cancel
Purpose: Handle cancelled payment callback from CyberSource
Authentication: None (callback from CyberSource)

This endpoint receives POST data from CyberSource and redirects to frontend.

================================================================================
3. CYBERSOURCE FLEX API (Capture Context)
================================================================================

3.1 Generate Capture Context
-----------------------------
URL: POST /api/website/cybersource/capture-context
Purpose: Generate capture context for CyberSource Flex tokenization
Authentication: Required

Request Payload:
{
  "target_origin": "http://localhost:3000"
}

Response:
{
  "success": true,
  "message": "Capture context generated successfully",
  "data": {
    "captureContext": "session_id_here",
    "flexSession": "session_id_here",
    "sessionId": "session_id_here",
    "fullResponse": {
      // Complete CyberSource response
    }
  }
}

3.2 Alternative Capture Context
--------------------------------
URL: POST /api/website/cybersource/capture-context-alt
Purpose: Alternative endpoint for capture context generation
Authentication: Required

Same request/response format as primary endpoint.

3.3 Client-based Capture Context
---------------------------------
URL: POST /api/website/cybersource/capture-context-client
Purpose: Generate capture context using existing CyberSourceClient
Authentication: Required

Request Payload:
{
  "organization_id": 1
}

Response: Same format as primary endpoint.

================================================================================
4. ORGANIZATION CURRENCY MANAGEMENT API (Within CheckOrganization Middleware)
================================================================================

4.1 List Organization Currencies
---------------------------------
URL: GET /api/admin/org-currencies?active_only=true
Purpose: Get currencies assigned to current organization
Authentication: Required
Middleware: CheckOrganization (auto-scoped by organization)

Query Parameters:
- active_only: boolean (optional) - Filter only active assignments

Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "organization_id": 1,
      "currency_id": 1,
      "is_default": true,
      "is_active": true,
      "custom_exchange_rate": null,
      "effective_exchange_rate": "1.00000",
      "currency": {
        "id": 1,
        "name": "US Dollar",
        "symbol": "$",
        "code": "USD",
        "status": "active",
        "exchange_rate": "1.00000"
      },
      "formatted_display": "US Dollar ($) - Rate: 1.00000 - Active (Default)",
      "created_at": "2023-12-21T10:30:45.000000Z",
      "updated_at": "2023-12-21T10:30:45.000000Z"
    }
  ],
  "organization_id": 1
}

4.2 Assign Currency to Organization
------------------------------------
URL: POST /api/admin/org-currencies/assign
Purpose: Assign a currency to current organization
Authentication: Required
Middleware: CheckOrganization

Request Payload:
{
  "currency_id": 3,
  "is_default": false,
  "is_active": true,
  "custom_exchange_rate": 0.75000
}

Response:
{
  "success": true,
  "message": "Currency assigned to organization successfully",
  "data": {
    "id": 3,
    "organization_id": 1,
    "currency_id": 3,
    "is_default": false,
    "is_active": true,
    "custom_exchange_rate": "0.75000",
    "effective_exchange_rate": "0.75000",
    "currency": {
      "id": 3,
      "name": "British Pound",
      "symbol": "£",
      "code": "GBP",
      "status": "active",
      "exchange_rate": "0.73000"
    },
    "formatted_display": "British Pound (£) - Rate: 0.75000 - Active",
    "created_at": "2023-12-21T12:30:45.000000Z",
    "updated_at": "2023-12-21T12:30:45.000000Z"
  }
}

4.3 Update Currency Assignment
-------------------------------
URL: PUT /api/admin/org-currencies/{id}
Purpose: Update organization currency assignment
Authentication: Required
Middleware: CheckOrganization

Request Payload:
{
  "is_default": true,
  "is_active": true,
  "custom_exchange_rate": 0.76000
}

Response: Same format as assign currency response.

4.4 Remove Currency Assignment
-------------------------------
URL: DELETE /api/admin/org-currencies/{id}
Purpose: Remove currency assignment from organization
Authentication: Required
Middleware: CheckOrganization

Response:
{
  "success": true,
  "message": "Currency assignment removed successfully"
}

4.5 Get Available Currencies
-----------------------------
URL: GET /api/admin/org-currencies/available
Purpose: Get currencies that can be assigned to organization
Authentication: Required
Middleware: CheckOrganization

Response:
{
  "success": true,
  "data": [
    {
      "id": 4,
      "name": "Japanese Yen",
      "symbol": "¥",
      "code": "JPY",
      "status": "active",
      "is_default": false,
      "exchange_rate": "110.00000",
      "created_at": "2023-12-21T10:30:45.000000Z",
      "updated_at": "2023-12-21T10:30:45.000000Z",
      "deleted_at": null
    }
  ],
  "organization_id": 1
}

================================================================================
AUTHENTICATION & MIDDLEWARE
================================================================================

Authentication:
- All admin endpoints require: Authorization: Bearer {your_auth_token}
- Public endpoints (visitor tracking, payment callbacks) don't require auth

CheckOrganization Middleware:
- Automatically scopes data by organization
- Detects organization from URL host or user's organization_id
- SystemAdmin/SuperAdmin can specify organization_id in requests
- Ensures complete multi-tenant data isolation

Error Responses:
- 401: Unauthenticated
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 500: Server Error

All APIs include comprehensive error handling and detailed logging.

================================================================================
FILES CREATED IN THIS SESSION
================================================================================

Backend Files:
1. app/Http/Controllers/VisitorTrackingController.php
2. app/Http/Controllers/OrganizationCurrencyController.php
3. app/Models/OrganizationCurrency.php
4. database/migrations/2024_01_01_000000_create_visitor_tracking_table.php
5. database/migrations/2024_01_02_000000_create_organization_currencies_table.php

Documentation Files:
1. VISITOR_TRACKING_API.md
2. ORGANIZATION_CURRENCY_API_ENDPOINTS.txt
3. CURRENCY_API_ENDPOINTS.txt
4. CYBERSOURCE_SECURE_ACCEPTANCE_REACT_GUIDE.txt (removed)
5. LATEST_APIS_DOCUMENTATION.txt (this file)

Routes Added:
- Visitor tracking routes in routes/website.php
- Organization currency routes in routes/admin.php
- CyberSource routes in routes/website.php

All APIs are production-ready with proper validation, error handling, and logging.
