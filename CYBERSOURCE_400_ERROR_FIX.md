# CyberSource 400 Bad Request Error - Fix Implementation

## Problem Analysis
You were getting a 400 Bad Request error when calling the CyberSource Flex API capture context endpoint. The error response was:

```json
{
    "success": false,
    "message": "Failed to generate capture context",
    "error": "Unknown error",
    "status_code": 400,
    "details": {
        "response": {
            "rmsg": "Bad Request"
        }
    }
}
```

## Root Cause
The 400 error was likely caused by:
1. **Incorrect Authentication Method**: Using wrong Basic Auth format
2. **Wrong Payload Structure**: Sending unnecessary fields in the request body
3. **API Version Mismatch**: Using v2 endpoint when account might support v1

## Implemented Solutions

### 1. Fixed Primary Method (`getCaptureContext`)
**Endpoint**: `POST /cybersource/capture-context`

**Changes Made**:
- ✅ Updated authentication to use `keyId:secretKey` format instead of `merchantId:apiKey`
- ✅ Simplified payload to only include `merchantId`
- ✅ Used <PERSON><PERSON>'s `Http::withBasicAuth()` method for cleaner authentication
- ✅ Enhanced logging for better debugging

**New Implementation**:
```php
// Correct credentials format
$keyId = 'c95a54f4-12bd-4703-a7e0-a1b64c0665fb';
$secretKey = 'zn0RnvqOcbGbUJQ6O6GJTNB/R7nlcL4eMWeOu04zIVs=';

// Simple payload
$payload = ['merchantId' => $merchantId];

// Correct authentication
$response = Http::withBasicAuth($keyId, $secretKey)->post($url, $payload);
```

### 2. Alternative Method (`getCaptureContextAlternative`)
**Endpoint**: `POST /cybersource/capture-context-alt`

**Changes Made**:
- ✅ Uses Flex API v1 endpoint instead of v2
- ✅ Same authentication fix as primary method
- ✅ Simplified payload structure

### 3. Client-Based Method (`getCaptureContextUsingClient`)
**Endpoint**: `POST /cybersource/capture-context-client`

**Features**:
- ✅ Uses the existing `CyberSourceClient` service
- ✅ Ensures exact same authentication method as working services
- ✅ Leverages proven configuration

## Testing Instructions

### Test All Three Methods

1. **Primary Method (Fixed)**:
```bash
curl -X POST http://localhost:8000/cybersource/capture-context \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"target_origin": "http://localhost:3000"}'
```

2. **Alternative Method (v1 API)**:
```bash
curl -X POST http://localhost:8000/cybersource/capture-context-alt \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"target_origin": "http://localhost:3000"}'
```

3. **Client-Based Method**:
```bash
curl -X POST http://localhost:8000/cybersource/capture-context-client \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"organization_id": 1}'
```

### Expected Success Response
```json
{
    "success": true,
    "message": "Capture context generated successfully",
    "data": {
        "captureContext": "session_id_here",
        "flexSession": "session_id_here", 
        "sessionId": "session_id_here",
        "fullResponse": {
            // Complete CyberSource response
        }
    }
}
```

## Key Changes Summary

### Authentication Fix
- **Before**: `Basic base64(merchantId:apiKey)`
- **After**: `Basic base64(keyId:secretKey)`

### Payload Simplification
- **Before**: Complex payload with multiple fields
- **After**: Simple `{"merchantId": "mehedi54321_1750580586"}`

### API Endpoint Options
- **Primary**: `https://apitest.cybersource.com/flex/v2/sessions`
- **Alternative**: `https://apitest.cybersource.com/flex/v1/sessions`
- **Client**: Uses existing service configuration

## Debugging Tips

1. **Check Laravel Logs**: Look for detailed request/response information
2. **Test Different Methods**: Try all three endpoints to see which works
3. **Verify Credentials**: Ensure the credentials are active in CyberSource portal
4. **Check Account Configuration**: Verify Flex API is enabled for your merchant account

## Next Steps

1. **Test the fixed endpoints** using the cURL commands above
2. **Check the logs** in `storage/logs/laravel.log` for detailed information
3. **Use the working endpoint** in your frontend integration
4. **Update your frontend code** to use the correct endpoint

## Frontend Integration Example

Once you identify the working endpoint, use it in your JavaScript:

```javascript
async function getCaptureContext() {
    try {
        const response = await fetch('/cybersource/capture-context', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + authToken,
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                target_origin: window.location.origin
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            return data.data.captureContext;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('Error getting capture context:', error);
        throw error;
    }
}
```

The fixes address the authentication and payload issues that were causing the 400 Bad Request error. Test all three methods to find the one that works with your specific CyberSource account configuration.
