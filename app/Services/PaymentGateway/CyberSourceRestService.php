<?php

namespace App\Services\PaymentGateway;

use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;

class CyberSourceRestService
{
    private $merchantId;
    private $keyId;
    private $secretKey;
    private $host;
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        // Test environment credentials - ensure these are valid and active
        $this->merchantId = 'mehedi54321_1750580586';
        $this->keyId = 'fb483a07-1fef-4150-8213-4ef8bab79f35';
        $this->secretKey = 'oL19+eJUaiMKlxKXY5U2EWl6oYrBFLkAYIhg1T4GrMY=';
        $this->host = 'https://apitest.cybersource.com';

        $this->logger = $logger;

        // Log the merchant ID being used for debugging
        $this->logger->info('CyberSource Service initialized', [
            'merchant_id' => $this->merchantId,
            'host' => $this->host,
            'key_id' => substr($this->keyId, 0, 8) . '...' // Only log first 8 chars for security
        ]);
    }

    public function createPayment(array $jsonBody): array
    {
        // Validate merchant credentials before making request
        if (empty($this->merchantId) || empty($this->keyId) || empty($this->secretKey)) {
            throw new \InvalidArgumentException('CyberSource credentials are not properly configured');
        }

        $client = new Client();
        $resource = '/pts/v2/payments';
        $fullUrl = $this->host . $resource;

        $jsonString = json_encode($jsonBody, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        $headers = $this->buildHeaders($resource, $jsonString, 'POST');

        $this->logger->info('CyberSource Request Details:', [
            'url' => $fullUrl,
            'method' => 'POST',
            'merchant_id' => $this->merchantId,
            'payload' => $jsonString
        ]);
        $this->logger->info('CyberSource Request Headers:', $headers);

        try {
            $response = $client->post($fullUrl, [
                'headers' => $headers,
                'json' => $jsonBody,
            ]);

            $responseBody = $response->getBody()->getContents();
            $this->logger->info('CyberSource Response:', [
                'status_code' => $response->getStatusCode(),
                'body' => $responseBody
            ]);

            return json_decode($responseBody, true);

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $statusCode = $e->getResponse()->getStatusCode();
            $body = (string) $e->getResponse()->getBody();
            $json = json_decode($body, true);

            $this->logger->error('CyberSource Payment Error:', [
                'status_code' => $statusCode,
                'url' => $fullUrl,
                'merchant_id' => $this->merchantId,
                'raw_response' => $body,
                'parsed_response' => $json
            ]);

            // Provide more specific error messages based on status code
            if ($statusCode === 404) {
                throw new \Exception('CyberSource API endpoint not found. This may indicate invalid merchant credentials or account configuration. Status: ' . $statusCode);
            }

            if (json_last_error() === JSON_ERROR_NONE && isset($json['message'])) {
                throw new \Exception('CyberSource API Error: ' . $json['message']);
            }

            throw $e;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $this->logger->error('CyberSource Request Error:', [
                'message' => $e->getMessage(),
                'url' => $fullUrl
            ]);
            throw $e;
        }
    }

    private function buildHeaders(string $resource, string $jsonString, string $method = 'POST'): array
    {
        $host = parse_url($this->host, PHP_URL_HOST);
        $vDate = gmdate('D, d M Y H:i:s T');
        $digest = base64_encode(hash('sha256', $jsonString, true));

        // Construct the signature string exactly as per CyberSource spec
        // Order is critical: host, v-c-date, request-target, digest, v-c-merchant-id
        $signatureString =
            "host: $host\n" .
            "v-c-date: $vDate\n" .
            "request-target: " . strtolower($method) . " $resource\n" .
            "digest: SHA-256=$digest\n" .
            "v-c-merchant-id: {$this->merchantId}";

        // Decode the secret key and generate signature
        $decodedSecret = base64_decode($this->secretKey, true);
        if ($decodedSecret === false) {
            throw new \InvalidArgumentException('Invalid base64 secret key');
        }

        $signature = base64_encode(hash_hmac('sha256', $signatureString, $decodedSecret, true));

        $this->logger->info('CyberSource Authentication Details:', [
            'host' => $host,
            'v_c_date' => $vDate,
            'request_target' => strtolower($method) . " $resource",
            'digest' => "SHA-256=$digest",
            'merchant_id' => $this->merchantId,
            'signature_string' => $signatureString,
            'signature' => $signature
        ]);

        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'v-c-merchant-id' => $this->merchantId,
            'v-c-date' => $vDate,
            'Host' => $host,
            'Digest' => "SHA-256=$digest",
            'Signature' => sprintf(
                'keyid="%s", algorithm="HmacSHA256", headers="host v-c-date request-target digest v-c-merchant-id", signature="%s"',
                $this->keyId,
                $signature
            ),
        ];
    }

    /**
     * Validate merchant credentials by making a test API call
     * This can help diagnose authentication issues
     */
    public function validateCredentials(): array
    {
        try {
            // Use a minimal payload for testing
            $testPayload = [
                "clientReferenceInformation" => ["code" => "test_validation"],
                "processingInformation" => ["commerceIndicator" => "internet"],
                "paymentInformation" => [
                    "card" => [
                        "number" => "****************",
                        "expirationMonth" => "12",
                        "expirationYear" => "2031"
                    ]
                ],
                "orderInformation" => [
                    "amountDetails" => [
                        "totalAmount" => "1.00",
                        "currency" => "USD"
                    ]
                ]
            ];

            $this->logger->info('Validating CyberSource credentials...');
            return $this->createPayment($testPayload);

        } catch (\Exception $e) {
            $this->logger->error('Credential validation failed:', [
                'error' => $e->getMessage(),
                'merchant_id' => $this->merchantId
            ]);
            throw $e;
        }
    }
}
