<?php

namespace App\Services\PaymentGateway;

use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;

class CyberSourceRestService
{
    private $merchantId;
    private $keyId;
    private $secretKey;
    private $host;
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        // Your test environment credentials here
        // $this->merchantId = 'mrueen2025_1748852609';
        // $this->keyId = '52bd1d57-e828-4de4-8ff8-7ad3468a9cab';
        // $this->secretKey = '1cbE6TwY57czPhw4F+1c3QWxEtX7crCgrvkJ40CywYc=';
        $this->merchantId = 'mehedi54321_1750580586';
        $this->keyId = 'fb483a07-1fef-4150-8213-4ef8bab79f35';
        $this->secretKey = 'oL19+eJUaiMKlxKXY5U2EWl6oYrBFLkAYIhg1T4GrMY=';
        $this->host = 'https://apitest.cybersource.com';

        $this->logger = $logger;
    }

    public function createPayment(array $jsonBody): array
    {
        $client = new Client();
        $resource = '/pts/v2/payments';

        $jsonString = json_encode($jsonBody, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        $headers = $this->buildHeaders($resource, $jsonString, 'POST');

        $this->logger->info('CyberSource Request Payload:', ['json' => $jsonString]);
        $this->logger->info('CyberSource Request Headers:', $headers);


        
        try {
            $response = $client->post($this->host . $resource, [
                'headers' => $headers,
                'json' => $jsonBody,
            ]);

            $responseBody = $response->getBody()->getContents();
            $this->logger->info('CyberSource Response:', ['body' => $responseBody]);

            return json_decode($responseBody, true);

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $body = (string) $e->getResponse()->getBody();
            $json = json_decode($body, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                $this->logger->error('CyberSource Payment Error:', $json);
            } else {
                $this->logger->error('CyberSource Payment Error (raw): ' . $body);
            }

            throw $e;
        }
    }

    private function buildHeaders(string $resource, string $jsonString, string $method = 'POST'): array
    {
        $host = parse_url($this->host, PHP_URL_HOST);
        $vDate = gmdate('D, d M Y H:i:s T');
        $digest = base64_encode(hash('sha256', $jsonString, true));

        // Construct the signature string exactly as per CyberSource spec
        $signatureString =
            "host: $host\n" .
            "v-c-date: $vDate\n" .
            "request-target: " . strtolower($method) . " $resource\n" .
            "digest: SHA-256=$digest\n" .
            "v-c-merchant-id: {$this->merchantId}";

        $decodedSecret = base64_decode($this->secretKey, true);
        $signature = base64_encode(hash_hmac('sha256', $signatureString, $decodedSecret, true));

        $this->logger->info('CyberSource Signature String:', ['string' => $signatureString]);
        $this->logger->info('Digest:', ['digest' => $digest]);
        $this->logger->info('Signature:', ['signature' => $signature]);

        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'v-c-merchant-id' => $this->merchantId,
            'v-c-date' => $vDate,
            'Host' => $host,
            'Digest' => "SHA-256=$digest",
            'Signature' => sprintf(
                'keyid="%s", algorithm="HmacSHA256", headers="host v-c-date request-target digest v-c-merchant-id", signature="%s"',
                $this->keyId,
                $signature
            ),
        ];
    }
}
