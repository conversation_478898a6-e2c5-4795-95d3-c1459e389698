<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CertificateTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'course_id',
        'background_image',
        'logo',
        'certification_text',
        'signature',
        'authorize_person',
        'designation',
        'prefix',
        'is_active'
    ];


    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }


    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id');
    }
}

