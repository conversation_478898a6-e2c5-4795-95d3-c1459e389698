<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrganizationCurrency extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'currency_id',
        'is_default',
        'is_active',
        'custom_exchange_rate',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'custom_exchange_rate' => 'decimal:5'
    ];

    /**
     * Get the organization that owns the currency assignment.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the currency that is assigned to the organization.
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Scope a query to only include active currency assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include default currency assignment.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get the effective exchange rate (custom or currency default).
     */
    public function getEffectiveExchangeRateAttribute()
    {
        return $this->custom_exchange_rate ?? $this->currency->exchange_rate;
    }

    /**
     * Get the formatted currency display with organization context.
     */
    public function getFormattedDisplayAttribute()
    {
        $rate = $this->effective_exchange_rate;
        $status = $this->is_active ? 'Active' : 'Inactive';
        $default = $this->is_default ? ' (Default)' : '';
        
        return "{$this->currency->name} ({$this->currency->symbol}) - Rate: {$rate} - {$status}{$default}";
    }
}
