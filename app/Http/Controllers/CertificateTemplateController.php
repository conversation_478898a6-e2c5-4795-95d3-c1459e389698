<?php

namespace App\Http\Controllers;

use App\Models\CertificateTemplate;
use Illuminate\Http\Request;
use App\Models\Course;
use Auth;
class CertificateTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function createOrUpdate(Request $request)
    {
        // Check if this is an update operation
        $isUpdate = $request->has('id') && !empty($request->id);

        // Validation rules
        $rules = [
            'course_id' => 'required|integer|exists:courses,id',
            'authorize_person' => 'nullable|string|max:255',
            'designation' => 'nullable|string|max:255',
            'certification_text' => 'nullable',
            'prefix' => 'nullable|string|max:255',
            'is_active' => 'boolean'
        ];

        // For update operations, make images optional
        if ($isUpdate) {
            $rules['id'] = 'required|integer|exists:certificate_templates,id';
            $rules['background_image'] = 'nullable|image|mimes:jpeg,png,jpg,gif,svg';
            $rules['logo'] = 'nullable|image|mimes:jpeg,png,jpg,gif,svg';
            $rules['signature'] = 'nullable|image|mimes:png,svg';
        } else {
            // For create operations, images are required
            $rules['background_image'] = 'required|image|mimes:jpeg,png,jpg,gif,svg';
            $rules['logo'] = 'required|image|mimes:jpeg,png,jpg,gif,svg';
            $rules['signature'] = 'required|image|mimes:png,svg';
        }

        $request->validate($rules);

        $course = Course::find($request->course_id);

        if (!$course) {
            return $this->apiResponse([], 'Course not found', false, 404);
        }

        $data = $request->only([
            'course_id',
            'authorize_person',
            'designation',
            'certification_text',
            'prefix',
            'is_active'
        ]);

        // Set organization_id from course
        $data['organization_id'] = $course->organization_id;

        // Handle image uploads only if files are provided
        if ($request->hasFile('background_image')) {
            $data['background_image'] = $this->imageUpload($request, 'background_image', 'certificate_bg');
        }

        if ($request->hasFile('signature')) {
            $data['signature'] = $this->imageUpload($request, 'signature', 'certificate_signature');
        }

        if ($request->hasFile('logo')) {
            $data['logo'] = $this->imageUpload($request, 'logo', 'certificate_logo');
        }

        if ($isUpdate) {
            // Update existing certificate template
            $certificateTemplate = CertificateTemplate::find($request->id);

            if (!$certificateTemplate) {
                return $this->apiResponse([], 'Certificate template not found', false, 404);
            }

            // Check if the template belongs to the same organization
            if ($certificateTemplate->organization_id !== $course->organization_id) {
                return $this->apiResponse([], 'Unauthorized access to certificate template', false, 403);
            }

            $certificateTemplate->update($data);

            return $this->apiResponse([
                'certificate_template' => $certificateTemplate->fresh()
            ], 'Certificate template updated successfully', true, 200);

        } else {
            // Create new certificate template
            $certificateTemplate = CertificateTemplate::create($data);

            return $this->apiResponse([
                'certificate_template' => $certificateTemplate
            ], 'Certificate template created successfully', true, 201);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(CertificateTemplate $certificateTemplate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CertificateTemplate $certificateTemplate)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CertificateTemplate $certificateTemplate)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CertificateTemplate $certificateTemplate)
    {
        //
    }
}
