<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use App\Models\OrganizationCurrency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class OrganizationCurrencyController extends Controller
{
    /**
     * Get list of currencies assigned to the current organization
     */
    public function index(Request $request)
    {
        try {
            $organizationId = $this->getOrganizationId();
            
            $query = OrganizationCurrency::with('currency')
                ->where('organization_id', $organizationId);

            // Filter by active status if requested
            if ($request->has('active_only') && $request->active_only) {
                $query->active();
            }

            $organizationCurrencies = $query->orderBy('is_default', 'desc')
                ->orderBy('created_at', 'desc')
                ->get();

            // Transform the data to include currency details
            $currencies = $organizationCurrencies->map(function ($orgCurrency) {
                return [
                    'id' => $orgCurrency->id,
                    'organization_id' => $orgCurrency->organization_id,
                    'currency_id' => $orgCurrency->currency_id,
                    'is_default' => $orgCurrency->is_default,
                    'is_active' => $orgCurrency->is_active,
                    'custom_exchange_rate' => $orgCurrency->custom_exchange_rate,
                    'effective_exchange_rate' => $orgCurrency->effective_exchange_rate,
                    'currency' => [
                        'id' => $orgCurrency->currency->id,
                        'name' => $orgCurrency->currency->name,
                        'symbol' => $orgCurrency->currency->symbol,
                        'code' => $orgCurrency->currency->code,
                        'status' => $orgCurrency->currency->status,
                        'exchange_rate' => $orgCurrency->currency->exchange_rate,
                    ],
                    'formatted_display' => $orgCurrency->formatted_display,
                    'created_at' => $orgCurrency->created_at,
                    'updated_at' => $orgCurrency->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $currencies,
                'organization_id' => $organizationId
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get organization currencies', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve organization currencies',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign a currency to the current organization
     */
    public function assignCurrency(Request $request)
    {
        try {
            $organizationId = $this->getOrganizationId();

            // Validate the request
            $validator = Validator::make($request->all(), [
                'currency_id' => 'required|exists:currencies,id',
                'is_default' => 'boolean',
                'is_active' => 'boolean',
                'custom_exchange_rate' => 'nullable|numeric|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Check if currency is already assigned to this organization
            $existingAssignment = OrganizationCurrency::where('organization_id', $organizationId)
                ->where('currency_id', $request->currency_id)
                ->first();

            if ($existingAssignment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Currency is already assigned to this organization',
                    'existing_assignment' => $existingAssignment
                ], 409);
            }

            // Check if currency exists and is active
            $currency = Currency::where('id', $request->currency_id)
                ->where('status', 'active')
                ->first();

            if (!$currency) {
                return response()->json([
                    'success' => false,
                    'message' => 'Currency not found or inactive'
                ], 404);
            }

            DB::beginTransaction();

            // If this is being set as default, remove default from other currencies
            if ($request->input('is_default', false)) {
                OrganizationCurrency::where('organization_id', $organizationId)
                    ->update(['is_default' => false]);
            }

            // Create the assignment
            $organizationCurrency = OrganizationCurrency::create([
                'organization_id' => $organizationId,
                'currency_id' => $request->currency_id,
                'is_default' => $request->input('is_default', false),
                'is_active' => $request->input('is_active', true),
                'custom_exchange_rate' => $request->custom_exchange_rate,
            ]);

            // Load the currency relationship
            $organizationCurrency->load('currency');

            DB::commit();

            Log::info('Currency assigned to organization', [
                'organization_id' => $organizationId,
                'currency_id' => $request->currency_id,
                'assignment_id' => $organizationCurrency->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Currency assigned to organization successfully',
                'data' => [
                    'id' => $organizationCurrency->id,
                    'organization_id' => $organizationCurrency->organization_id,
                    'currency_id' => $organizationCurrency->currency_id,
                    'is_default' => $organizationCurrency->is_default,
                    'is_active' => $organizationCurrency->is_active,
                    'custom_exchange_rate' => $organizationCurrency->custom_exchange_rate,
                    'effective_exchange_rate' => $organizationCurrency->effective_exchange_rate,
                    'currency' => $organizationCurrency->currency,
                    'formatted_display' => $organizationCurrency->formatted_display,
                    'created_at' => $organizationCurrency->created_at,
                    'updated_at' => $organizationCurrency->updated_at,
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to assign currency to organization', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to assign currency to organization',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update organization currency assignment
     */
    public function updateAssignment(Request $request, $id)
    {
        try {
            $organizationId = $this->getOrganizationId();

            // Validate the request
            $validator = Validator::make($request->all(), [
                'is_default' => 'boolean',
                'is_active' => 'boolean',
                'custom_exchange_rate' => 'nullable|numeric|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Find the assignment
            $organizationCurrency = OrganizationCurrency::where('id', $id)
                ->where('organization_id', $organizationId)
                ->first();

            if (!$organizationCurrency) {
                return response()->json([
                    'success' => false,
                    'message' => 'Currency assignment not found'
                ], 404);
            }

            DB::beginTransaction();

            // If this is being set as default, remove default from other currencies
            if ($request->has('is_default') && $request->is_default) {
                OrganizationCurrency::where('organization_id', $organizationId)
                    ->where('id', '!=', $id)
                    ->update(['is_default' => false]);
            }

            // Update the assignment
            $organizationCurrency->update($request->only([
                'is_default',
                'is_active',
                'custom_exchange_rate'
            ]));

            // Load the currency relationship
            $organizationCurrency->load('currency');

            DB::commit();

            Log::info('Organization currency assignment updated', [
                'assignment_id' => $id,
                'organization_id' => $organizationId,
                'updated_fields' => $request->only(['is_default', 'is_active', 'custom_exchange_rate'])
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Currency assignment updated successfully',
                'data' => [
                    'id' => $organizationCurrency->id,
                    'organization_id' => $organizationCurrency->organization_id,
                    'currency_id' => $organizationCurrency->currency_id,
                    'is_default' => $organizationCurrency->is_default,
                    'is_active' => $organizationCurrency->is_active,
                    'custom_exchange_rate' => $organizationCurrency->custom_exchange_rate,
                    'effective_exchange_rate' => $organizationCurrency->effective_exchange_rate,
                    'currency' => $organizationCurrency->currency,
                    'formatted_display' => $organizationCurrency->formatted_display,
                    'created_at' => $organizationCurrency->created_at,
                    'updated_at' => $organizationCurrency->updated_at,
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update organization currency assignment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'assignment_id' => $id,
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update currency assignment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove currency assignment from organization
     */
    public function removeAssignment($id)
    {
        try {
            $organizationId = $this->getOrganizationId();

            // Find the assignment
            $organizationCurrency = OrganizationCurrency::where('id', $id)
                ->where('organization_id', $organizationId)
                ->first();

            if (!$organizationCurrency) {
                return response()->json([
                    'success' => false,
                    'message' => 'Currency assignment not found'
                ], 404);
            }

            // Check if this is the default currency
            if ($organizationCurrency->is_default) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot remove default currency. Please set another currency as default first.'
                ], 400);
            }

            $organizationCurrency->delete();

            Log::info('Currency assignment removed from organization', [
                'assignment_id' => $id,
                'organization_id' => $organizationId,
                'currency_id' => $organizationCurrency->currency_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Currency assignment removed successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to remove organization currency assignment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'assignment_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to remove currency assignment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available currencies that can be assigned to the organization
     */
    public function getAvailableCurrencies()
    {
        try {
            $organizationId = $this->getOrganizationId();

            // Get currencies that are not already assigned to this organization
            $assignedCurrencyIds = OrganizationCurrency::where('organization_id', $organizationId)
                ->pluck('currency_id')
                ->toArray();

            $availableCurrencies = Currency::where('status', 'active')
                ->whereNotIn('id', $assignedCurrencyIds)
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $availableCurrencies,
                'organization_id' => $organizationId
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get available currencies', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve available currencies',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the current organization ID from authenticated user or context
     */
    private function getOrganizationId()
    {
        if (Auth::check()) {
            $user = Auth::user();
            
            // SystemAdmin and SuperAdmin can work with any organization
            if (in_array($user->user_type, ['SystemAdmin', 'SuperAdmin'])) {
                // For admin users, we might need to get organization from request or default to 1
                return request()->input('organization_id', 1);
            }
            
            return $user->organization_id;
        }
        
        // Fallback to organization ID 1 if no user is authenticated
        return 1;
    }
}
