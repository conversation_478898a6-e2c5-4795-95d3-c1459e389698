<?php

namespace App\Http\Controllers;

use App\Http\Traits\HelperTrait;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class FileUploadController extends Controller
{
    use HelperTrait;

    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Upload file to AWS S3 using the service
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadToS3(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|max:102400', // 100MB max
                'folder' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors(), 'Validation failed', 422);
            }

            $result = $this->fileUploadService->uploadFile(
                $request, 
                'file', 
                $request->input('folder'), 
                null, 
                true // Use S3
            );

            if (!$result) {
                return $this->errorResponse([], 'File upload failed', 500);
            }

            return $this->successResponse($result, 'File uploaded to S3 successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()], 'File upload failed', 500);
        }
    }

    /**
     * Upload file to local storage using the service
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadToLocal(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|max:102400', // 100MB max
                'folder' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors(), 'Validation failed', 422);
            }

            $result = $this->fileUploadService->uploadFile(
                $request, 
                'file', 
                $request->input('folder'), 
                null, 
                false // Use local storage
            );

            if (!$result) {
                return $this->errorResponse([], 'File upload failed', 500);
            }

            return $this->successResponse($result, 'File uploaded to local storage successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()], 'File upload failed', 500);
        }
    }

    /**
     * Upload image using the HelperTrait method (AWS S3)
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadImageS3(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:10240', // 10MB max
                'folder' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors(), 'Validation failed', 422);
            }

            $result = $this->imageUpload(
                $request, 
                'image', 
                $request->input('folder'), 
                null, 
                true // Use AWS S3
            );

            if (!$result) {
                return $this->errorResponse([], 'Image upload failed', 500);
            }

            return $this->successResponse($result, 'Image uploaded to S3 successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()], 'Image upload failed', 500);
        }
    }

    /**
     * Upload image using the HelperTrait method (Local)
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadImageLocal(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:10240', // 10MB max
                'folder' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors(), 'Validation failed', 422);
            }

            $result = $this->imageUpload(
                $request, 
                'image', 
                $request->input('folder'), 
                null, 
                false // Use local storage
            );

            if (!$result) {
                return $this->errorResponse([], 'Image upload failed', 500);
            }

            return $this->successResponse($result, 'Image uploaded to local storage successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()], 'Image upload failed', 500);
        }
    }

    /**
     * Delete file from S3
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteFromS3(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file_path' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors(), 'Validation failed', 422);
            }

            $deleted = $this->fileUploadService->deleteFromS3($request->input('file_path'));

            if ($deleted) {
                return $this->successResponse(['deleted' => true], 'File deleted from S3 successfully');
            } else {
                return $this->errorResponse(['deleted' => false], 'File not found or deletion failed', 404);
            }

        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()], 'File deletion failed', 500);
        }
    }

    /**
     * Get signed URL for private S3 file
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getSignedUrl(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file_path' => 'required|string',
                'expiration_minutes' => 'nullable|integer|min:1|max:10080', // Max 1 week
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors(), 'Validation failed', 422);
            }

            $expirationMinutes = $request->input('expiration_minutes', 60);
            $signedUrl = $this->fileUploadService->getSignedUrl(
                $request->input('file_path'), 
                $expirationMinutes
            );

            if ($signedUrl) {
                return $this->successResponse([
                    'signed_url' => $signedUrl,
                    'expires_in_minutes' => $expirationMinutes
                ], 'Signed URL generated successfully');
            } else {
                return $this->errorResponse([], 'File not found or URL generation failed', 404);
            }

        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()], 'Signed URL generation failed', 500);
        }
    }

    /**
     * Check if file exists in S3
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function checkFileExists(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file_path' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors(), 'Validation failed', 422);
            }

            $exists = $this->fileUploadService->fileExistsInS3($request->input('file_path'));
            $size = null;

            if ($exists) {
                $size = $this->fileUploadService->getFileSizeFromS3($request->input('file_path'));
            }

            return $this->successResponse([
                'exists' => $exists,
                'file_path' => $request->input('file_path'),
                'size_bytes' => $size
            ], 'File existence check completed');

        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()], 'File existence check failed', 500);
        }
    }

    /**
     * Upload multiple files to S3
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadMultipleFilesEndpoint(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'files' => 'required|array|max:10', // Max 10 files
                'files.*' => 'required|file|max:51200', // 50MB max per file
                'folder' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse($validator->errors(), 'Validation failed', 422);
            }

            $results = [];
            $errors = [];

            foreach ($request->file('files') as $index => $file) {
                try {
                    $result = $this->fileUploadService->uploadFileDirect(
                        $file, 
                        $request->input('folder'), 
                        null, 
                        true // Use S3
                    );

                    if ($result) {
                        $results[] = $result;
                    } else {
                        $errors[] = "File {$index}: Upload failed";
                    }
                } catch (\Exception $e) {
                    $errors[] = "File {$index}: " . $e->getMessage();
                }
            }

            if (empty($results) && !empty($errors)) {
                return $this->errorResponse(['errors' => $errors], 'All file uploads failed', 500);
            }

            $response = ['uploaded_files' => $results];
            if (!empty($errors)) {
                $response['errors'] = $errors;
            }

            return $this->successResponse($response, 'File upload process completed');

        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()], 'Multiple file upload failed', 500);
        }
    }
}
