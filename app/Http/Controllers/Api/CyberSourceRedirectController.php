<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

use App\Services\PaymentGateway\CyberSourceRestService;

class CyberSourceRedirectController extends Controller
{

    protected $cyberSource;

    public function __construct(CyberSourceRestService $cyberSource)
    {
        $this->cyberSource = $cyberSource;
    }

    public function checkout(Request $request)
    {
        $accessKey = config('cybersource.sa_access_key');
        $secretKey = config('cybersource.sa_secret_key');
        $profileId = config('cybersource.sa_profile_id');
        $transaction_uuid = uniqid();
        $signed_date_time = gmdate("Y-m-d\TH:i:s\Z");

        $fields = [
            "access_key" => $accessKey,
            "profile_id" => $profileId,
            "transaction_uuid" => $transaction_uuid,
            "signed_field_names" => "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency",
            "unsigned_field_names" => "",
            "signed_date_time" => $signed_date_time,
            "locale" => "en",
            "transaction_type" => "sale",
            "reference_number" => "ORDER-" . now()->timestamp,
            "amount" => $request->amount ?? "100.00",
            "currency" => $request->currency ?? "USD",
        ];

        $fields['signature'] = $this->sign($fields, $secretKey);

        return view('cybersource.redirect', compact('fields'));
    }

    public function generate(Request $request)
    {
        $url = 'https://ebc2test.cybersource.com/ebc2/payByLink/pay/educourse21928';
        // $url = 'https://ebc2test.cybersource.com/ebc2/payByLink/pay/coursepurchase78';
        
        return response()->json(['link' => $url]);
    }


    public function getRedirectData(Request $request)
    {
        $accessKey = '1a7eb7aaceeb37348f760b11bfe74673'; // config('cybersource.sa_access_key');
        $secretKey = '6a13dc667dfb4dc586777c0e575cc43b3d81f92205ce4286b2b749b179a2b67dacce3f011adf427eaff8c50c24164e65843e97b5b27c44888a2c458bc87cf91fd9d5b48293ec469fa56d973f6b372672a0803d5f0de64294b87cf8d1692d3628c60d1a4717ae4f48a86bd5cac6f7e4b70aea81907ca94760bb2bf4b8db67f465'; // config('cybersource.sa_secret_key');
        $profileId = 'mrueen_2025_1748852656'; // config('cybersource.sa_profile_id');
        $uuid = uniqid();
        $signedDateTime = gmdate("Y-m-d\TH:i:s\Z");

        $fields = [
            "access_key" => $accessKey,
            "profile_id" => $profileId,
            "transaction_uuid" => $uuid,
            "signed_field_names" => "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency",
            "unsigned_field_names" => "",
            "signed_date_time" => $signedDateTime,
            "locale" => "en",
            "transaction_type" => "sale",
            "reference_number" => "ORDER-" . now()->timestamp,
            "amount" => $request->amount ?? "100.00",
            "currency" => $request->currency ?? "USD",
        ];

        $fields['signature'] = $this->sign($fields, $secretKey);

        return response()->json($fields);
    }

    private function sign(array $params, $secretKey)
    {
        $signedFieldNames = explode(",", $params["signed_field_names"]);
        $dataToSign = [];
        foreach ($signedFieldNames as $field) {
            $dataToSign[] = $field . "=" . $params[$field];
        }
        $data = implode(",", $dataToSign);
        return base64_encode(hash_hmac('sha256', $data, $secretKey, true));
    }



    /**
     * Generate a CyberSource Pay-by-Link URL
     */
    public function generateCyberSourceLink(Request $request)
    {
        $amount =  100; // number_format($request->amount, 2, '.', '');

        $fields = [
            "access_key" => 'fdfd07fcf53d3bc5b58a43e0fe08c0d8',
            "profile_id" => 'educourse21928',
            "transaction_uuid" => (string) Str::uuid(),
            "signed_field_names" => "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,amount,currency,locale,override_custom_receipt_page,override_custom_cancel_page",
            "unsigned_field_names" => "",
            "amount" => $amount,
            "currency" => "USD",
            "locale" => "en",
            "override_custom_receipt_page" => route('cybersource.success'),
            "override_custom_cancel_page" => route('cybersource.cancel'),
        ];
        $secretKey = '4e73dc7b0f8a47c0a8640fda44d9d45e0e6a60f21aa145848c83e64339566f483e07c6b6d7fe4f3c8282ad48decac9d7503dd95c896c46c188fa5d4da23a4216859d787c9dc340aeaa1a9bb3b8895b4d56ae12ad6ca84445966773aff27afd0a479e7fe8fb2146ff996e5396940819e6059bee25dbb44ffb8bd606554db1f514';
        $fields['signature'] = $this->generateSignature($fields, $secretKey);

        return response()->json([
            'url' => "https://testsecureacceptance.cybersource.com/pay",
            'params' => $fields,
        ]);
    }

    /**
     * Handle successful redirect from CyberSource
     */
    public function handleSuccess(Request $request)
    {
        Log::info('CyberSource Success Response:', $request->all());

        // Optional: Verify signature here
        // You can redirect to your frontend app with query params:
        return redirect()->to('https://your-frontend-app.com/payment-success');
    }

    /**
     * Handle cancelled payment
     */
    public function handleCancel(Request $request)
    {
        Log::warning('CyberSource Cancelled:', $request->all());

        return redirect()->to('https://your-frontend-app.com/payment-cancel');
    }

    /**
     * Generate CyberSource HMAC signature
     */
    private function generateSignature(array $params, string $secretKey): string
    {
        ksort($params);

        $signedFieldNames = explode(',', $params['signed_field_names']);
        $dataToSign = [];

        foreach ($signedFieldNames as $field) {
            $dataToSign[] = $field . '=' . $params[$field];
        }

        $signature = base64_encode(
            hash_hmac('sha256', implode(',', $dataToSign), $secretKey, true)
        );

        return $signature;
    }


    public function payByRESTApi(Request $request)
    {
        try {
            $data = $request->all();
            Log::info('CyberSource REST API Payment Request:', $data);

            $jsonBody = [
                "clientReferenceInformation" => ["code" => "test_payment_" . uniqid()],
                "processingInformation" => ["commerceIndicator" => "internet"],
                "paymentInformation" => [
                    "card" => [
                        "number" => "****************",
                        "expirationMonth" => "12",
                        "expirationYear" => "2031"
                    ]
                ],
                "orderInformation" => [
                    "amountDetails" => [
                        "totalAmount" => "102.21",
                        "currency" => "USD"
                    ],
                    "billTo" => [
                        "firstName" => "John",
                        "lastName" => "Doe",
                        "address1" => "1 Market St",
                        "locality" => "san francisco",
                        "administrativeArea" => "CA",
                        "postalCode" => "94105",
                        "country" => "US",
                        "email" => "<EMAIL>",
                        "phoneNumber" => "4158880000"
                    ]
                ]
            ];

            Log::info('Attempting CyberSource payment with payload:', $jsonBody);
            $response = $this->cyberSource->createPayment($jsonBody);

            Log::info('CyberSource payment successful:', $response);
            return response()->json([
                'success' => true,
                'data' => $response
            ]);

        } catch (\Exception $e) {
            Log::error('CyberSource payment failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Payment processing failed. Please check the logs for details.'
            ], 500);
        }
    }

    /**
     * Test CyberSource credentials and connection
     */
    public function testCredentials(Request $request)
    {
        try {
            Log::info('Testing CyberSource credentials...');

            // Try to validate credentials with a minimal test payment
            $response = $this->cyberSource->validateCredentials();

            return response()->json([
                'success' => true,
                'message' => 'CyberSource credentials are valid',
                'data' => $response
            ]);

        } catch (\Exception $e) {
            Log::error('CyberSource credential test failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'CyberSource credentials test failed. Please check the configuration.'
            ], 500);
        }
    }

}
