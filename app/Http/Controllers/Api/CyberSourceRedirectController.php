<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;

use App\Services\PaymentGateway\CyberSourceRestService;
use App\Services\CyberSourceClient;

class CyberSourceRedirectController extends Controller
{

    protected $cyberSource;

    public function __construct(CyberSourceRestService $cyberSource)
    {
        $this->cyberSource = $cyberSource;
    }

    public function checkout(Request $request)
    {
        $accessKey = config('cybersource.sa_access_key');
        $secretKey = config('cybersource.sa_secret_key');
        $profileId = config('cybersource.sa_profile_id');
        $transaction_uuid = uniqid();
        $signed_date_time = gmdate("Y-m-d\TH:i:s\Z");

        $fields = [
            "access_key" => $accessKey,
            "profile_id" => $profileId,
            "transaction_uuid" => $transaction_uuid,
            "signed_field_names" => "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency",
            "unsigned_field_names" => "",
            "signed_date_time" => $signed_date_time,
            "locale" => "en",
            "transaction_type" => "sale",
            "reference_number" => "ORDER-" . now()->timestamp,
            "amount" => $request->amount ?? "100.00",
            "currency" => $request->currency ?? "USD",
        ];

        $fields['signature'] = $this->sign($fields, $secretKey);

        return view('cybersource.redirect', compact('fields'));
    }

    public function generate(Request $request)
    {
        $url = 'https://ebc2test.cybersource.com/ebc2/payByLink/pay/educourse21928';
        // $url = 'https://ebc2test.cybersource.com/ebc2/payByLink/pay/coursepurchase78';
        
        return response()->json(['link' => $url]);
    }


    public function getRedirectData(Request $request)
    {
        $accessKey = '1a7eb7aaceeb37348f760b11bfe74673'; // config('cybersource.sa_access_key');
        $secretKey = '6a13dc667dfb4dc586777c0e575cc43b3d81f92205ce4286b2b749b179a2b67dacce3f011adf427eaff8c50c24164e65843e97b5b27c44888a2c458bc87cf91fd9d5b48293ec469fa56d973f6b372672a0803d5f0de64294b87cf8d1692d3628c60d1a4717ae4f48a86bd5cac6f7e4b70aea81907ca94760bb2bf4b8db67f465'; // config('cybersource.sa_secret_key');
        $profileId = 'mrueen_2025_1748852656'; // config('cybersource.sa_profile_id');
        $uuid = uniqid();
        $signedDateTime = gmdate("Y-m-d\TH:i:s\Z");

        $fields = [
            "access_key" => $accessKey,
            "profile_id" => $profileId,
            "transaction_uuid" => $uuid,
            "signed_field_names" => "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency",
            "unsigned_field_names" => "",
            "signed_date_time" => $signedDateTime,
            "locale" => "en",
            "transaction_type" => "sale",
            "reference_number" => "ORDER-" . now()->timestamp,
            "amount" => $request->amount ?? "100.00",
            "currency" => $request->currency ?? "USD",
        ];

        $fields['signature'] = $this->sign($fields, $secretKey);

        return response()->json($fields);
    }

    private function sign(array $params, $secretKey)
    {
        $signedFieldNames = explode(",", $params["signed_field_names"]);
        $dataToSign = [];
        foreach ($signedFieldNames as $field) {
            $dataToSign[] = $field . "=" . $params[$field];
        }
        $data = implode(",", $dataToSign);
        return base64_encode(hash_hmac('sha256', $data, $secretKey, true));
    }



    /**
     * Generate a CyberSource Pay-by-Link URL
     */
    public function generateCyberSourceLink(Request $request)
    {
        $amount =  100; // number_format($request->amount, 2, '.', '');

        $fields = [
            "access_key" => 'fdfd07fcf53d3bc5b58a43e0fe08c0d8',
            "profile_id" => 'educourse21928',
            "transaction_uuid" => (string) Str::uuid(),
            "signed_field_names" => "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,amount,currency,locale,override_custom_receipt_page,override_custom_cancel_page",
            "unsigned_field_names" => "",
            "amount" => $amount,
            "currency" => "USD",
            "locale" => "en",
            "override_custom_receipt_page" => route('cybersource.success'),
            "override_custom_cancel_page" => route('cybersource.cancel'),
        ];
        $secretKey = '4e73dc7b0f8a47c0a8640fda44d9d45e0e6a60f21aa145848c83e64339566f483e07c6b6d7fe4f3c8282ad48decac9d7503dd95c896c46c188fa5d4da23a4216859d787c9dc340aeaa1a9bb3b8895b4d56ae12ad6ca84445966773aff27afd0a479e7fe8fb2146ff996e5396940819e6059bee25dbb44ffb8bd606554db1f514';
        $fields['signature'] = $this->generateSignature($fields, $secretKey);

        return response()->json([
            'url' => "https://testsecureacceptance.cybersource.com/pay",
            'params' => $fields,
        ]);
    }

    /**
     * Handle successful redirect from CyberSource
     */
    public function handleSuccess(Request $request)
    {
        Log::info('CyberSource Success Response:', $request->all());

        // Optional: Verify signature here
        // You can redirect to your frontend app with query params:
        return redirect()->to('https://your-frontend-app.com/payment-success');
    }

    /**
     * Handle cancelled payment
     */
    public function handleCancel(Request $request)
    {
        Log::warning('CyberSource Cancelled:', $request->all());

        return redirect()->to('https://your-frontend-app.com/payment-cancel');
    }

    /**
     * Generate CyberSource HMAC signature
     */
    private function generateSignature(array $params, string $secretKey): string
    {
        ksort($params);

        $signedFieldNames = explode(',', $params['signed_field_names']);
        $dataToSign = [];

        foreach ($signedFieldNames as $field) {
            $dataToSign[] = $field . '=' . $params[$field];
        }

        $signature = base64_encode(
            hash_hmac('sha256', implode(',', $dataToSign), $secretKey, true)
        );

        return $signature;
    }


    public function payByRESTApi(Request $request)
    {
        try {
            $data = $request->all();
            Log::info('CyberSource REST API Payment Request:', $data);

            $jsonBody = [
                "clientReferenceInformation" => ["code" => "test_payment_" . uniqid()],
                "processingInformation" => ["commerceIndicator" => "internet"],
                "paymentInformation" => [
                    "card" => [
                        "number" => "****************",
                        "expirationMonth" => "12",
                        "expirationYear" => "2031"
                    ]
                ],
                "orderInformation" => [
                    "amountDetails" => [
                        "totalAmount" => "102.21",
                        "currency" => "USD"
                    ],
                    "billTo" => [
                        "firstName" => "John",
                        "lastName" => "Doe",
                        "address1" => "1 Market St",
                        "locality" => "san francisco",
                        "administrativeArea" => "CA",
                        "postalCode" => "94105",
                        "country" => "US",
                        "email" => "<EMAIL>",
                        "phoneNumber" => "4158880000"
                    ]
                ]
            ];

            Log::info('Attempting CyberSource payment with payload:', $jsonBody);
            $response = $this->cyberSource->createPayment($jsonBody);

            Log::info('CyberSource payment successful:', $response);
            return response()->json([
                'success' => true,
                'data' => $response
            ]);

        } catch (\Exception $e) {
            Log::error('CyberSource payment failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Payment processing failed. Please check the logs for details.'
            ], 500);
        }
    }

    /**
     * Test CyberSource credentials and connection
     */
    public function testCredentials(Request $request)
    {
        try {
            Log::info('Testing CyberSource credentials...');

            // Try to validate credentials with a minimal test payment
            $response = $this->cyberSource->validateCredentials();

            return response()->json([
                'success' => true,
                'message' => 'CyberSource credentials are valid',
                'data' => $response
            ]);

        } catch (\Exception $e) {
            Log::error('CyberSource credential test failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'CyberSource credentials test failed. Please check the configuration.'
            ], 500);
        }
    }

    /**
     * Run comprehensive diagnostic tests for CyberSource integration
     */
    public function runDiagnostics(Request $request)
    {
        try {
            Log::info('Running CyberSource diagnostic tests...');

            $diagnostics = $this->cyberSource->diagnosticTest();

            return response()->json([
                'success' => true,
                'message' => 'Diagnostic tests completed',
                'diagnostics' => $diagnostics,
                'recommendations' => $this->generateRecommendations($diagnostics)
            ]);

        } catch (\Exception $e) {
            Log::error('CyberSource diagnostic test failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Diagnostic tests failed.'
            ], 500);
        }
    }

    /**
     * Generate recommendations based on diagnostic results
     */
    private function generateRecommendations(array $diagnostics): array
    {
        $recommendations = [];

        if (isset($diagnostics['payment_test']) && !$diagnostics['payment_test']['success']) {
            $statusCode = $diagnostics['payment_test']['status_code'] ?? 0;

            if ($statusCode === 404) {
                $recommendations[] = [
                    'priority' => 'HIGH',
                    'issue' => 'REST API Access Not Enabled',
                    'description' => 'Your merchant account may not be configured for REST API access.',
                    'actions' => [
                        'Log into your CyberSource Business Center',
                        'Navigate to Account Management > Digital Payment Solutions',
                        'Verify that REST API services are enabled',
                        'Contact CyberSource support if REST API is not available',
                        'Provide them with your Merchant ID: mehedi54321_1750580586'
                    ]
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Generate CyberSource Flex Capture Context for secure card tokenization
     * This is equivalent to the JavaScript getCaptureContext function
     */
    public function getCaptureContext(Request $request)
    {
        try {
            // CyberSource credentials - using the same format as CyberSourceClient
            $merchantId = 'mehedi54321_1750580586';
            $keyId = 'c95a54f4-12bd-4703-a7e0-a1b64c0665fb';
            $secretKey = 'zn0RnvqOcbGbUJQ6O6GJTNB/R7nlcL4eMWeOu04zIVs=';

            // Use test environment URL for Flex API v2
            $url = 'https://apitest.cybersource.com/flex/v2/sessions';

            // Get target origin from request or use default
            $targetOrigin = $request->input('target_origin', 'http://localhost:4001');

            // Prepare the payload - simple merchantId only for Flex API
            $payload = [
                'merchantId' => $merchantId
            ];

            Log::info('CyberSource Flex - Generating capture context', [
                'merchant_id' => $merchantId,
                'target_origin' => $targetOrigin,
                'url' => $url,
                'payload' => $payload,
                'key_id' => substr($keyId, 0, 8) . '...'
            ]);

            // Use Basic Auth with keyId and secretKey (same as CyberSourceClient)
            $response = Http::withBasicAuth($keyId, $secretKey)
                ->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info('CyberSource Flex - Capture context generated successfully', [
                    'session_id' => $responseData['sessionId'] ?? 'N/A'
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Capture context generated successfully',
                    'data' => [
                        'captureContext' => $responseData['sessionId'] ?? null,
                        'flexSession' => $responseData['sessionId'] ?? null, // For compatibility
                        'sessionId' => $responseData['sessionId'] ?? null,
                        'fullResponse' => $responseData
                    ]
                ]);
            } else {
                $errorData = $response->json();

                Log::error('CyberSource Flex - Failed to generate capture context', [
                    'status_code' => $response->status(),
                    'error_response' => $errorData
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate capture context',
                    'error' => $errorData['message'] ?? 'Unknown error',
                    'status_code' => $response->status(),
                    'details' => $errorData
                ], $response->status());
            }

        } catch (\Exception $e) {
            Log::error('CyberSource Flex - Exception occurred', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while generating capture context',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Alternative method using different endpoint structure
     * Some CyberSource accounts might use different Flex API endpoints
     */
    public function getCaptureContextAlternative(Request $request)
    {
        try {
            // CyberSource credentials
            $merchantId = 'mehedi54321_1750580586';
            $keyId = 'c95a54f4-12bd-4703-a7e0-a1b64c0665fb';
            $secretKey = 'zn0RnvqOcbGbUJQ6O6GJTNB/R7nlcL4eMWeOu04zIVs=';

            // Try Flex API v1 endpoint as alternative
            $url = 'https://apitest.cybersource.com/flex/v1/sessions';

            $targetOrigin = $request->input('target_origin', 'http://localhost:3000');

            // Simple payload for v1 API
            $payload = [
                'merchantId' => $merchantId
            ];

            Log::info('CyberSource Flex Alternative - Generating capture context', [
                'merchant_id' => $merchantId,
                'url' => $url,
                'payload' => $payload
            ]);

            // Use Basic Auth with keyId and secretKey
            $response = Http::withBasicAuth($keyId, $secretKey)
                ->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();

                return response()->json([
                    'success' => true,
                    'message' => 'Capture context generated successfully (alternative endpoint)',
                    'data' => [
                        'captureContext' => $responseData['sessionId'] ?? null,
                        'flexSession' => $responseData['sessionId'] ?? null,
                        'sessionId' => $responseData['sessionId'] ?? null,
                        'fullResponse' => $responseData
                    ]
                ]);
            } else {
                $errorData = $response->json();

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate capture context (alternative endpoint)',
                    'error' => $errorData['message'] ?? 'Unknown error',
                    'status_code' => $response->status(),
                    'details' => $errorData
                ], $response->status());
            }

        } catch (\Exception $e) {
            Log::error('CyberSource Flex Alternative - Exception occurred', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while generating capture context',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate capture context using the existing CyberSourceClient service
     * This ensures we use the exact same authentication method
     */
    public function getCaptureContextUsingClient(Request $request)
    {
        try {
            Log::info('CyberSource Flex - Using existing CyberSourceClient service');

            // Use the existing CyberSourceClient with organization ID 1 (or get from request)
            $organizationId = $request->input('organization_id', 1);
            $cyberSourceClient = new CyberSourceClient($organizationId);

            // Call the createSession method
            $response = $cyberSourceClient->createSession();

            Log::info('CyberSource Flex - Session created successfully using client', [
                'response' => $response
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Capture context generated successfully using CyberSourceClient',
                'data' => [
                    'captureContext' => $response['sessionId'] ?? null,
                    'flexSession' => $response['sessionId'] ?? null,
                    'sessionId' => $response['sessionId'] ?? null,
                    'fullResponse' => $response
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('CyberSource Flex - Client method failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate capture context using CyberSourceClient',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate Secure Acceptance payment form data
     * This creates the form fields and signature for hosted payment
     */
    public function generateSecureAcceptancePayment(Request $request)
    {
        try {
            // Validate required fields
            $request->validate([
                'amount' => 'required|numeric|min:0.01',
                'currency' => 'required|string|size:3',
                'reference_number' => 'required|string|max:50',
                'customer_email' => 'required|email',
                'customer_name' => 'required|string|max:100'
            ]);

            // CyberSource Secure Acceptance credentials
            $accessKey = 'ad36521de6533073afc8626952c910b1';
            $secretKey = '670419a54f7048ce93423c4862d1a7ec115a5605b2d64946b43d4614dcb55cf808e1e7927f664664a18b71e39ff9563569211ad4fcfe41e19d83c88b462312047d8fe89376fe4d9b916c331f0e407c9043d086149ce6459e81eb5baed4b0cdc6bdf361bd338947caa263c5403b8e8f6d629e414e26744e8a84d7922167a4363e';
            $profileId = 'mehedi54321_1750580586'; // Using merchant ID as profile ID

            // Generate unique transaction UUID
            $transactionUuid = (string) Str::uuid();
            $signedDateTime = gmdate("Y-m-d\TH:i:s\Z");

            // Prepare form fields
            $fields = [
                'access_key' => $accessKey,
                'profile_id' => $profileId,
                'transaction_uuid' => $transactionUuid,
                'signed_field_names' => 'access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency,customer_email,bill_to_forename,bill_to_surname,bill_to_email',
                'unsigned_field_names' => '',
                'signed_date_time' => $signedDateTime,
                'locale' => 'en',
                'transaction_type' => 'sale',
                'reference_number' => $request->reference_number,
                'amount' => number_format($request->amount, 2, '.', ''),
                'currency' => strtoupper($request->currency),
                'customer_email' => $request->customer_email,
                'bill_to_forename' => explode(' ', $request->customer_name)[0] ?? $request->customer_name,
                'bill_to_surname' => explode(' ', $request->customer_name, 2)[1] ?? '',
                'bill_to_email' => $request->customer_email,
            ];

            // Add optional fields if provided
            if ($request->has('customer_phone')) {
                $fields['bill_to_phone'] = $request->customer_phone;
                $fields['signed_field_names'] .= ',bill_to_phone';
            }

            if ($request->has('billing_address')) {
                $address = $request->billing_address;
                $fields['bill_to_address_line1'] = $address['line1'] ?? '';
                $fields['bill_to_address_city'] = $address['city'] ?? '';
                $fields['bill_to_address_state'] = $address['state'] ?? '';
                $fields['bill_to_address_postal_code'] = $address['postal_code'] ?? '';
                $fields['bill_to_address_country'] = $address['country'] ?? 'US';
                $fields['signed_field_names'] .= ',bill_to_address_line1,bill_to_address_city,bill_to_address_state,bill_to_address_postal_code,bill_to_address_country';
            }

            // Generate signature
            $fields['signature'] = $this->generateSecureAcceptanceSignature($fields, $secretKey);

            Log::info('CyberSource Secure Acceptance - Payment form generated', [
                'transaction_uuid' => $transactionUuid,
                'amount' => $fields['amount'],
                'currency' => $fields['currency'],
                'reference_number' => $fields['reference_number']
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Secure Acceptance payment form generated successfully',
                'data' => [
                    'form_fields' => $fields,
                    'action_url' => 'https://testsecureacceptance.cybersource.com/pay',
                    'method' => 'POST',
                    'transaction_uuid' => $transactionUuid
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('CyberSource Secure Acceptance - Payment form generation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate payment form',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate HMAC signature for Secure Acceptance
     */
    private function generateSecureAcceptanceSignature(array $params, string $secretKey): string
    {
        $signedFieldNames = explode(',', $params['signed_field_names']);
        $dataToSign = [];

        foreach ($signedFieldNames as $field) {
            if (isset($params[$field])) {
                $dataToSign[] = $field . '=' . $params[$field];
            }
        }

        $dataString = implode(',', $dataToSign);
        $signature = base64_encode(hash_hmac('sha256', $dataString, $secretKey, true));

        Log::info('CyberSource Secure Acceptance - Signature generated', [
            'signed_fields' => $signedFieldNames,
            'data_string' => $dataString,
            'signature' => $signature
        ]);

        return $signature;
    }

    /**
     * Handle Secure Acceptance success callback
     */
    public function handleSecureAcceptanceSuccess(Request $request)
    {
        try {
            Log::info('CyberSource Secure Acceptance - Success callback received', $request->all());

            // Verify the signature to ensure the response is authentic
            $secretKey = '670419a54f7048ce93423c4862d1a7ec115a5605b2d64946b43d4614dcb55cf808e1e7927f664664a18b71e39ff9563569211ad4fcfe41e19d83c88b462312047d8fe89376fe4d9b916c331f0e407c9043d086149ce6459e81eb5baed4b0cdc6bdf361bd338947caa263c5403b8e8f6d629e414e26744e8a84d7922167a4363e';

            if ($this->verifySecureAcceptanceSignature($request->all(), $secretKey)) {
                // Process successful payment
                $paymentData = [
                    'transaction_id' => $request->transaction_id,
                    'req_transaction_uuid' => $request->req_transaction_uuid,
                    'decision' => $request->decision,
                    'reason_code' => $request->reason_code,
                    'amount' => $request->req_amount,
                    'currency' => $request->req_currency,
                    'auth_code' => $request->auth_code ?? null,
                    'payment_token' => $request->payment_token ?? null
                ];

                // Here you would typically save the payment to your database
                // and redirect the user to a success page

                return redirect()->to(config('app.frontend_url') . '/payment/success?' . http_build_query([
                    'transaction_id' => $paymentData['transaction_id'],
                    'amount' => $paymentData['amount'],
                    'status' => 'success'
                ]));

            } else {
                Log::error('CyberSource Secure Acceptance - Invalid signature in success callback');
                return redirect()->to(config('app.frontend_url') . '/payment/error?reason=invalid_signature');
            }

        } catch (\Exception $e) {
            Log::error('CyberSource Secure Acceptance - Success callback error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return redirect()->to(config('app.frontend_url') . '/payment/error?reason=processing_error');
        }
    }

    /**
     * Handle Secure Acceptance cancel/error callback
     */
    public function handleSecureAcceptanceCancel(Request $request)
    {
        try {
            Log::info('CyberSource Secure Acceptance - Cancel callback received', $request->all());

            return redirect()->to(config('app.frontend_url') . '/payment/cancel?' . http_build_query([
                'transaction_uuid' => $request->req_transaction_uuid ?? '',
                'reason' => $request->message ?? 'Payment cancelled by user'
            ]));

        } catch (\Exception $e) {
            Log::error('CyberSource Secure Acceptance - Cancel callback error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return redirect()->to(config('app.frontend_url') . '/payment/error?reason=callback_error');
        }
    }

    /**
     * Verify Secure Acceptance signature
     */
    private function verifySecureAcceptanceSignature(array $params, string $secretKey): bool
    {
        if (!isset($params['signature']) || !isset($params['signed_field_names'])) {
            return false;
        }

        $receivedSignature = $params['signature'];
        $signedFieldNames = explode(',', $params['signed_field_names']);
        $dataToSign = [];

        foreach ($signedFieldNames as $field) {
            if (isset($params[$field])) {
                $dataToSign[] = $field . '=' . $params[$field];
            }
        }

        $dataString = implode(',', $dataToSign);
        $expectedSignature = base64_encode(hash_hmac('sha256', $dataString, $secretKey, true));

        return hash_equals($expectedSignature, $receivedSignature);
    }

    /**
     * Test Secure Acceptance configuration
     */
    public function testSecureAcceptance(Request $request)
    {
        try {
            // Test data
            $testData = [
                'amount' => '10.00',
                'currency' => 'USD',
                'reference_number' => 'TEST-' . time(),
                'customer_email' => '<EMAIL>',
                'customer_name' => 'Test User'
            ];

            Log::info('CyberSource Secure Acceptance - Running test', $testData);

            // Create a test request
            $testRequest = new Request($testData);

            // Generate payment form
            $response = $this->generateSecureAcceptancePayment($testRequest);

            if ($response->getStatusCode() === 200) {
                $responseData = json_decode($response->getContent(), true);

                return response()->json([
                    'success' => true,
                    'message' => 'Secure Acceptance test successful',
                    'test_data' => $testData,
                    'generated_form' => $responseData['data'] ?? null,
                    'instructions' => [
                        '1. Copy the form_fields from the response',
                        '2. Create an HTML form with action_url',
                        '3. Add all form_fields as hidden inputs',
                        '4. Submit the form to test payment flow'
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Secure Acceptance test failed',
                    'error' => 'Failed to generate payment form'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('CyberSource Secure Acceptance - Test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Secure Acceptance test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

}
