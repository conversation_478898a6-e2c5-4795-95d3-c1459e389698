<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class VisitorTrackingController extends Controller
{
    /**
     * Track a visitor's page visit
     */
    public function trackVisitor(Request $request)
    {
        try {
            // Validate the incoming request
            $validator = Validator::make($request->all(), [
                'page_name' => 'required|string|max:100',
                'page_url' => 'required|url|max:2000',
                'page_path' => 'required|string|max:500',
                'session_id' => 'required|string|max:100',
                'visitor_id' => 'required|string|max:100',
                'browser_name' => 'nullable|string|max:50',
                'browser_version' => 'nullable|string|max:50',
                'user_agent' => 'nullable|string|max:1000',
                'device_type' => 'nullable|string|max:20',
                'operating_system' => 'nullable|string|max:50',
                'screen_width' => 'nullable|integer|min:0',
                'screen_height' => 'nullable|integer|min:0',
                'viewport_width' => 'nullable|integer|min:0',
                'viewport_height' => 'nullable|integer|min:0',
                'timezone' => 'nullable|string|max:100',
                'language' => 'nullable|string|max:10',
                'referrer' => 'nullable|url|max:2000',
                'utm_source' => 'nullable|string|max:100',
                'utm_medium' => 'nullable|string|max:100',
                'utm_campaign' => 'nullable|string|max:100',
                'utm_term' => 'nullable|string|max:100',
                'utm_content' => 'nullable|string|max:100',
                'visited_at' => 'required|date',
                'additional_data' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Check if this visitor has already been tracked for this session and page
            $existingTracking = DB::table('visitor_tracking')
                ->where('session_id', $request->session_id)
                ->where('page_name', $request->page_name)
                ->where('created_at', '>=', Carbon::now()->subHours(1)) // Within last hour
                ->first();

            if ($existingTracking) {
                return response()->json([
                    'success' => true,
                    'message' => 'Visitor already tracked for this session',
                    'data' => ['tracking_id' => $existingTracking->id]
                ]);
            }

            // Prepare data for insertion
            $trackingData = [
                'page_name' => $request->page_name,
                'page_url' => $request->page_url,
                'page_path' => $request->page_path,
                'session_id' => $request->session_id,
                'visitor_id' => $request->visitor_id,
                'browser_name' => $request->browser_name,
                'browser_version' => $request->browser_version,
                'user_agent' => $request->user_agent,
                'device_type' => $request->device_type,
                'operating_system' => $request->operating_system,
                'screen_width' => $request->screen_width,
                'screen_height' => $request->screen_height,
                'viewport_width' => $request->viewport_width,
                'viewport_height' => $request->viewport_height,
                'timezone' => $request->timezone,
                'language' => $request->language,
                'referrer' => $request->referrer,
                'utm_source' => $request->utm_source,
                'utm_medium' => $request->utm_medium,
                'utm_campaign' => $request->utm_campaign,
                'utm_term' => $request->utm_term,
                'utm_content' => $request->utm_content,
                'additional_data' => $request->additional_data ? json_encode($request->additional_data) : null,
                'visited_at' => Carbon::parse($request->visited_at),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];

            // Insert the tracking data
            $trackingId = DB::table('visitor_tracking')->insertGetId($trackingData);

            Log::info('Visitor tracked successfully', [
                'tracking_id' => $trackingId,
                'page_name' => $request->page_name,
                'visitor_id' => $request->visitor_id,
                'session_id' => $request->session_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Visitor tracked successfully',
                'data' => ['tracking_id' => $trackingId]
            ]);

        } catch (\Exception $e) {
            Log::error('Visitor tracking failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to track visitor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get visitor statistics for admin dashboard
     */
    public function getVisitorStats(Request $request)
    {
        try {
            $dateRange = $request->input('date_range', 7); // Default to last 7 days
            $startDate = Carbon::now()->subDays($dateRange);

            // Total visitors
            $totalVisitors = DB::table('visitor_tracking')
                ->where('created_at', '>=', $startDate)
                ->distinct('visitor_id')
                ->count();

            // Total page views
            $totalPageViews = DB::table('visitor_tracking')
                ->where('created_at', '>=', $startDate)
                ->count();

            // Page popularity
            $pageStats = DB::table('visitor_tracking')
                ->select('page_name', DB::raw('COUNT(*) as views'), DB::raw('COUNT(DISTINCT visitor_id) as unique_visitors'))
                ->where('created_at', '>=', $startDate)
                ->groupBy('page_name')
                ->orderBy('views', 'desc')
                ->get();

            // Device type distribution
            $deviceStats = DB::table('visitor_tracking')
                ->select('device_type', DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('device_type')
                ->groupBy('device_type')
                ->get();

            // Browser distribution
            $browserStats = DB::table('visitor_tracking')
                ->select('browser_name', DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('browser_name')
                ->groupBy('browser_name')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get();

            // Daily visitor trend
            $dailyStats = DB::table('visitor_tracking')
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as page_views'),
                    DB::raw('COUNT(DISTINCT visitor_id) as unique_visitors')
                )
                ->where('created_at', '>=', $startDate)
                ->groupBy(DB::raw('DATE(created_at)'))
                ->orderBy('date')
                ->get();

            // UTM source analysis
            $utmStats = DB::table('visitor_tracking')
                ->select('utm_source', DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('utm_source')
                ->groupBy('utm_source')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => [
                        'total_visitors' => $totalVisitors,
                        'total_page_views' => $totalPageViews,
                        'date_range' => $dateRange
                    ],
                    'page_stats' => $pageStats,
                    'device_stats' => $deviceStats,
                    'browser_stats' => $browserStats,
                    'daily_stats' => $dailyStats,
                    'utm_stats' => $utmStats
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get visitor stats', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve visitor statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get detailed visitor list for admin review
     */
    public function getVisitorList(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 50);
            $page = $request->input('page', 1);
            $pageName = $request->input('page_name');
            $dateRange = $request->input('date_range', 7);

            $query = DB::table('visitor_tracking')
                ->select([
                    'id',
                    'page_name',
                    'page_url',
                    'visitor_id',
                    'session_id',
                    'browser_name',
                    'browser_version',
                    'device_type',
                    'operating_system',
                    'timezone',
                    'language',
                    'referrer',
                    'utm_source',
                    'utm_medium',
                    'utm_campaign',
                    'visited_at',
                    'created_at'
                ])
                ->where('created_at', '>=', Carbon::now()->subDays($dateRange));

            if ($pageName) {
                $query->where('page_name', $pageName);
            }

            $total = $query->count();
            $visitors = $query
                ->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'visitors' => $visitors,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => ceil($total / $perPage)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get visitor list', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve visitor list',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test visitor tracking endpoint
     */
    public function testVisitorTracking(Request $request)
    {
        try {
            // Sample visitor data for testing
            $testData = [
                'page_name' => 'home',
                'page_url' => 'http://localhost:8000/',
                'page_path' => '/',
                'session_id' => 'session_' . time() . '_test',
                'visitor_id' => 'visitor_' . time() . '_test',
                'browser_name' => 'Chrome',
                'browser_version' => '120.0.6099.109',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'device_type' => 'Desktop',
                'operating_system' => 'Windows',
                'screen_width' => 1920,
                'screen_height' => 1080,
                'viewport_width' => 1536,
                'viewport_height' => 864,
                'timezone' => 'America/New_York',
                'language' => 'en-US',
                'referrer' => 'https://google.com',
                'utm_source' => 'google',
                'utm_medium' => 'cpc',
                'utm_campaign' => 'test_campaign',
                'utm_term' => 'lms_software',
                'utm_content' => 'ad_variant_a',
                'visited_at' => now()->toISOString(),
                'additional_data' => [
                    'page_type' => 'landing',
                    'section' => 'promotional',
                    'test_mode' => true
                ]
            ];

            // Create a test request
            $testRequest = new Request($testData);

            // Call the trackVisitor method
            $response = $this->trackVisitor($testRequest);
            $responseData = json_decode($response->getContent(), true);

            if ($response->getStatusCode() === 200) {
                return response()->json([
                    'success' => true,
                    'message' => 'Visitor tracking test completed successfully',
                    'test_data' => $testData,
                    'tracking_response' => $responseData,
                    'instructions' => [
                        '1. The visitor tracking API is working correctly',
                        '2. Test data has been inserted into the database',
                        '3. You can now integrate this API with your frontend',
                        '4. Use POST /website/track-visitor to track real visitors'
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Visitor tracking test failed',
                    'error' => $responseData,
                    'test_data' => $testData
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Visitor tracking test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Visitor tracking test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
