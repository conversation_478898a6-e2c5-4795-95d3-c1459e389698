<?php

namespace App\Http\Traits;

use App\Models\Organization;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Payment;
use App\Models\StudentInformation;
use App\Models\ClassSchedule;
use App\Models\BatchStudent;
use App\Models\StudentAssignment;
use App\Models\AssignmentSubmission;
use App\Models\StudentJoinHistory;
use App\Models\VideoWatchLog;
use App\Models\ScriptViewLog;
use App\Models\ChapterQuizResult;
use App\Http\Controllers\Mobile\ContentWatchLogController;

use App\Http\Resources\Mobile\ClassSheduleResource;

trait CourseDetailsSerializeTrait {

    public function getCourseDetails($request, $course)
    {
        $user = auth('sanctum')->user();
        $isStudent = $user?->user_type === 'Student';

        $completedItems = [];
        if ($isStudent) {
            $completedVideos = VideoWatchLog::where('course_id', $course->id)
                ->where('user_id', $user->id)
                ->where('is_watched', 1)
                ->pluck('course_outline_id')
                ->toArray();

            $completedScripts = ScriptViewLog::where('course_id', $course->id)
                ->where('user_id', $user->id)
                ->where('is_watched', 1)
                ->pluck('course_outline_id')
                ->toArray();

            $completedTests = ChapterQuizResult::where('chapter_quiz_results.course_id', $course->id)
                ->where('chapter_quiz_results.user_id', $user->id)
                ->where('submission_status', 'Submitted')
                ->join('course_outlines', 'chapter_quiz_results.chapter_quiz_id', '=', 'course_outlines.chapter_quiz_id')
                ->pluck('course_outlines.id')
                ->toArray();

            $completedItems = array_unique(array_merge($completedVideos, $completedScripts, $completedTests));
        }

        $data = [
            "id" => $course->id,
            "category_id" => $course->category_id,
            "category_name" => $course->category?->name,
            "title" => $course->title,
            "title_bn" => $course->title_bn,
            "description" => $course->description,
            "thumbnail" => $course->thumbnail,
            "trailer_video" => $course->youtube_url ? explode('=', $course->youtube_url)[1] : null,
            "number_of_enrolled" => $course->number_of_enrolled,
            "regular_price" => $course->regular_price,
            "sale_price" => $course->sale_price,
            "show_price" => $course->show_price ? true : false,
            "currency" => $course->currency,
            "minimum_enroll_amount" => $course->minimum_enroll_amount,
            "installment_type" => $course->installment_type,
            "monthly_amount" => $course->monthly_amount,
            "max_installment_qty" => $course->max_installment_qty,
            "discount_percentage" => $course->discount_percentage,
            "has_life_coach" => $course->has_life_coach,
            "course_duration" => $course->course_duration,
            "duration_per_day" => $course->duration_per_day,
            "is_free" => $course->is_free,
            "is_active" => $course->is_active,
            "is_featured" => $course->is_featured,
            "created_by" => $course->created_by,
            "created_at" => $course->created_at->format("Y-m-d H:i:s"),
            "updated_at" => $course->updated_at->format("Y-m-d H:i:s"),
            "external_libraries" => $course->externalLibrary,
            "rating" => $course->rating,
            "rating_count" => $course->courseRatings->count(),
            "rating_list" => $course->courseRatings->take(4)->map(function ($r) {
                return [
                    'id' => $r->id,
                    'rating' => $r->rating,
                    'comment' => $r->comment,
                    'user' => [
                        'id' => $r->user_id,
                        'name' => $r->user?->name
                    ]
                ];
            }),
            "features" => $course->courseFeature->map->only(['id', 'title', 'title_bn', 'icon']),
            "prerequisites" => $course->coursePrerequisites->map->only(['id', 'title', 'title_bn', 'icon']),
            "learning_outcomes" => $course->courseLearningItems->map->only(['id', 'title', 'title_bn', 'icon']),
            "mentors" => $course->courseMentor->map(function ($mentor) {
                return [
                    'id' => $mentor?->mentor?->id,
                    'name' => $mentor?->mentor?->name,
                    'profession' => $mentor?->mentor?->profession,
                    'education' => $mentor?->mentor?->education,
                    'institute' => $mentor?->mentor?->institute,
                    'image' => $mentor?->mentor?->image ? $mentor->mentor->image : 'user.png'
                ];
            }),
            "faqs" => $course->courseFaq->map->only(['id', 'title', 'answer']),
            "related_courses" => $course->relatedCourses->map(function ($rel) {
                return [
                    'id' => $rel->id,
                    'title' => $rel->title,
                    'thumbnail' => $rel->thumbnail,
                    'mentors' => $rel->courseMentor->map(function ($mentor) {
                        return [
                            'id' => $mentor?->mentor?->id,
                            'name' => $mentor?->mentor?->name,
                            'image' => $mentor?->mentor?->image ? $mentor?->mentor?->image : 'user.png'
                        ];
                    }),
                ];
            }),
        ];

        // If student, add progress & enrollment info
        if ($isStudent) {
            $data['is_enrolled'] = false;
            $data['is_enrolled_pending'] = false;

            $payment = Payment::where('user_id', $user->id)->where('item_id', $course->id)->first();
            if ($payment) {
                if ($payment->is_approved == 1) {
                    $data['is_enrolled'] = true;

                    $student = StudentInformation::where('user_id', $user->id)->first();

                    $totalItems = $course->courseOutline->count()
                        + $course->assignments->count()
                        + $course->liveClasses->count();

                    $quizParticipation = $course->chapterQuizResults
                        ->where('user_id', $user->id)
                        ->where('submission_status', 'Submitted')
                        ->unique('chapter_quiz_id')
                        ->count();

                    $uniqueAssignments = AssignmentSubmission::where('student_id', $student->id)
                        ->where('course_id', $course->id)
                        ->distinct('assignment_id')
                        ->count('assignment_id');

                    $joinedClasses = StudentJoinHistory::where('student_id', $student->id)
                        ->distinct('class_schedule_id')
                        ->count('assignment_id');

                    $completedItemsCount = count($completedItems) + $quizParticipation + $uniqueAssignments + $joinedClasses;

                    $data['progress'] = $totalItems ? round(($completedItemsCount / $totalItems) * 100) : 0;

                    // Latest activity
                    $data['latest_activities'] = (new ContentWatchLogController($request))
                        ->getCourseLastAccessedContent($user, $course->id);

                    // Assignments
                    $assignments = StudentAssignment::select('assignments.*')
                        ->leftJoin('assignments', 'assignments.id', '=', 'student_assignments.assignment_id')
                        ->where('student_assignments.student_id', $student->id)
                        ->where('assignments.status', '!=', 'Unpublished')
                        ->where('assignments.course_id', $course->id)
                        ->limit(1)
                        ->get();

                    $data['assignments'] = $assignments->map(function ($a) {
                        return [
                            'id' => $a->id,
                            'title' => $a->title,
                            'is_active' => (bool) $a->is_active,
                            'publish_date' => $a->publish_date,
                            'deadline' => $a->deadline
                        ];
                    });



                    $ongoingClass = ClassSchedule::join('class_schedule_students', 'class_schedules.id', '=', 'class_schedule_students.class_schedule_id')
                        ->where('class_schedule_students.student_id', $student->id)
                        ->where('class_schedules.course_id', $course->id)
                        ->where('class_schedules.schedule_datetime', '>=', now()->format('Y-m-d H:i:s'))
                        ->first();

                    $data['ongoing_class'] = $ongoingClass;


                    $classScheduleIdList = ClassSchedule::join('class_schedule_students', 'class_schedules.id', '=', 'class_schedule_students.class_schedule_id')
                        ->where('class_schedule_students.student_id', $student->id)
                        ->where('class_schedules.course_id', $course->id)
                        ->whereNull('class_schedules.batch_id')
                        ->pluck('class_schedules.id');

                    $batch = BatchStudent::where('student_id', $student->id)->with(['batch', 'batch.classSchedules'])->first();

                    $batchClassList = $batch?->batch?->classSchedules ?? collect(); // Ensure it's a collection
                    $classList = ClassSchedule::whereIn('id', $classScheduleIdList)->get();

                    // Merge both collections and remove duplicates by ID
                    $mergedClassList = $batchClassList->merge($classList)->unique('id')->values();

                    // Return as resource collection
                    $data['class_schedules'] = ClassSheduleResource::collection($mergedClassList);

                } else {
                    $data['is_enrolled_pending'] = true;
                }
            }
        }

        // Process outlines with sequential unlocking logic
        if ($isStudent) {
            $isFirstOutline = true;

            // --- SUBJECTS LOGIC START ---
            $data['subjects'] = $course->subjects->map(function ($subject) use ($completedItems) {
                return [
                    'color_code' => $subject->color_code,
                    'course_id' => $subject->course_id,
                    'icon' => $subject->icon,
                    'name' => $subject->name,
                    'name_bn' => $subject->name_bn,
                    'subject_code' => $subject->subject_code,
                    'outlines' => $subject->courseCategory->map(function ($category) use ($completedItems) {
                        // Build contents for each outline (category)
                        $contents = $category->courseOutlines->where('is_active', 1)->map(function ($outline) use ($completedItems) {
                            return [
                                'id' => $outline->id,
                                'title' => $outline->title,
                                'video_id' => $outline->chapter_video_id,
                                'script_id' => $outline->chapter_script_id,
                                'quiz_id' => $outline->chapter_quiz_id,
                                'type' => $outline->chapter_video_id ? 'video' : ($outline->chapter_script_id ? 'script' : 'quiz'),
                                'is_free' => $outline->is_free,
                                'is_completed' => in_array($outline->id, $completedItems),
                            ];
                        });


                        return [
                            'id' => $category->id,
                            'name' => $category->name,
                            'name_bn' => $category->name_bn ?? null,
                            'is_active' => $category->is_active,
                            'videos_number' => $category->courseOutlines->whereNotNull('chapter_video_id')->count(),
                            'scripts_number' => $category->courseOutlines->whereNotNull('chapter_script_id')->count(),
                            'quizs_number' => $category->courseOutlines->whereNotNull('chapter_quiz_id')->count(),
                            'is_completed' => false, // or your logic
                            'contents' => $contents,
                        ];
                    })
                ];
            });
            // --- SUBJECTS LOGIC END ---

            // Get all outlines across all categories in sequence order
            $allOutlines = $course->courseCategory
                ->whereNull('subject_id')
                ->sortBy('sequence')
                ->flatMap(function ($category) use ($completedItems) {
                    return $category->courseOutlines->where('is_active', 1)->sortBy('sequence')->map(function ($outline) use ($completedItems, $category) {
                        return [
                            'id' => $outline->id,
                            'title' => $outline->title,
                            'video_id' => $outline->chapter_video_id,
                            'script_id' => $outline->chapter_script_id,
                            'quiz_id' => $outline->chapter_quiz_id,
                            'type' => $outline->chapter_video_id ? 'video' : ($outline->chapter_script_id ? 'script' : 'quiz'),
                            'is_free' => $outline->is_free,
                            'is_completed' => in_array($outline->id, $completedItems),
                            'category_id' => $category->id,
                            'category_name' => $category->name,
                            'category_name_bn' => $category->name_bn,
                            'category_is_active' => $category->is_active,
                        ];
                    });
                });

            // Apply sequential locking logic across all outlines
            $previousCompleted = true; // First item should be unlocked
            $processedOutlines = [];

            foreach ($allOutlines as $index => $outline) {
                $isLocked = !$outline['is_free'] && !$previousCompleted;
                $processedOutlines[] = array_merge($outline, [
                    'is_locked' => $isLocked
                ]);

                // Update previousCompleted for next iteration
                $previousCompleted = $outline['is_completed'] || $outline['is_free'];
            }

            // Group back by categories
            $data['outlines'] = $course->courseCategory
                ->whereNull('subject_id')
                ->sortBy('sequence')
                ->map(function ($category) use ($processedOutlines) {
                    $categoryOutlines = collect($processedOutlines)->where('category_id', $category->id)->values();

                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'name_bn' => $category->name_bn,
                        'is_active' => $category->is_active,
                        'videos_number' => $categoryOutlines->whereNotNull('video_id')->count(),
                        'scripts_number' => $categoryOutlines->whereNotNull('script_id')->count(),
                        'quizs_number' => $categoryOutlines->whereNotNull('quiz_id')->count(),
                        'total_items' => $categoryOutlines->count(),
                        'contents' => $categoryOutlines->map(function ($outline) {
                            return collect($outline)->except(['category_id', 'category_name', 'category_name_bn', 'category_is_active'])->toArray();
                        })->values()
                    ];
                })
                ->values();
        } else {
            // For non-students, keep the original logic
            $data['outlines'] = $course->courseCategory->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'name_bn' => $category->name_bn,
                    'is_active' => $category->is_active,
                    'videos_number' => $category->courseOutlines->whereNotNull('chapter_video_id')->count(),
                    'scripts_number' => $category->courseOutlines->whereNotNull('chapter_script_id')->count(),
                    'quizs_number' => $category->courseOutlines->whereNotNull('chapter_quiz_id')->count(),
                    'total_items' => $category->courseOutlines->count(),
                    'contents' => $category->courseOutlines->map(function ($outline) {
                        return [
                            'id' => $outline->id,
                            'title' => $outline->title,
                            'video_id' => $outline->chapter_video_id,
                            'script_id' => $outline->chapter_script_id,
                            'quiz_id' => $outline->chapter_quiz_id,
                            'type' => $outline->chapter_video_id ? 'video' : ($outline->chapter_script_id ? 'script' : 'quiz'),
                            'is_free' => $outline->is_free,
                            'is_locked' => !$outline->is_free,
                            'is_completed' => false,
                        ];
                    })
                ];
            });
        }

        return $data;
    }
}
