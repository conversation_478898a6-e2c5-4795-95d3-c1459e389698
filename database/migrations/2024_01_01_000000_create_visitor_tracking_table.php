<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('visitor_tracking', function (Blueprint $table) {
            $table->id();
            
            // Page Information
            $table->string('page_name', 100)->index();
            $table->text('page_url');
            $table->string('page_path', 500);
            
            // Session Information
            $table->string('session_id', 100)->index();
            $table->string('visitor_id', 100)->index();
            
            // Browser Information
            $table->string('browser_name', 50)->nullable();
            $table->string('browser_version', 50)->nullable();
            $table->text('user_agent')->nullable();
            
            // Device Information
            $table->string('device_type', 20)->nullable();
            $table->string('operating_system', 50)->nullable();
            
            // Screen Information
            $table->integer('screen_width')->nullable();
            $table->integer('screen_height')->nullable();
            $table->integer('viewport_width')->nullable();
            $table->integer('viewport_height')->nullable();
            
            // Location Information
            $table->string('timezone', 100)->nullable();
            $table->string('language', 10)->nullable();
            
            // Referrer Information
            $table->text('referrer')->nullable();
            $table->string('utm_source', 100)->nullable();
            $table->string('utm_medium', 100)->nullable();
            $table->string('utm_campaign', 100)->nullable();
            $table->string('utm_term', 100)->nullable();
            $table->string('utm_content', 100)->nullable();
            
            // Additional Data (JSON for flexibility)
            $table->json('additional_data')->nullable();
            
            // Timestamps
            $table->timestamp('visited_at')->index();
            $table->timestamps();
            
            // Composite indexes for better query performance
            $table->index(['page_name', 'visited_at']);
            $table->index(['visitor_id', 'created_at']);
            $table->index(['session_id', 'page_name']);
            $table->index(['created_at', 'page_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('visitor_tracking');
    }
};
