<?php

use App\Http\Controllers\AnnouncementController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\ContentController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\MasterSettingsController;
use App\Http\Controllers\MentorController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\CourseRatingController;
use App\Http\Controllers\BatchController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\DiscussionController;
use App\Http\Controllers\PaymentTypeOrganizationController;
use App\Http\Controllers\TestimonialController;
use App\Http\Controllers\Mobile\ContentWatchLogController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\LabelTranslationController;
use App\Http\Controllers\Mobile\CategoryController  as MobileCategoryController;
use App\Http\Controllers\Mobile\MentorController as MobileMentorController;
use App\Http\Controllers\Mobile\StudentController as MobileStudentController;
use App\Http\Controllers\Mobile\ContentController as MobileContentController;
use App\Http\Controllers\Mobile\CourseController as MobileCourseController;
use App\Http\Controllers\Mobile\OrganizationController as MobileOrganizationController;
use App\Http\Controllers\Mobile\PaymentController as MobilePaymentController;
use App\Http\Controllers\Mobile\ClassScheduleController;
use App\Http\Controllers\OrganizationPaymentController;
use App\Http\Controllers\CouponController;
use App\Http\Middleware\AuthCheckMiddleware as AuthCheck;
use App\Http\Middleware\CheckOrganization;
use App\Http\Middleware\ContentProtectedMiddleware as checkContent;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Api\CyberSourceRedirectController;


//open route for website
Route::get('templates', [TemplateController::class, 'templateDetails']);
Route::get('organization-details', [OrganizationController::class, 'getOrganizationDetails']);
Route::get('content-details/{content_id}', [ContentController::class, 'contentDetailsByID']);
// Route::get('course-details/{course_id}', [CourseController::class, 'courseDetailsByUserID'])->middleware([AuthCheck::class]);
Route::post('organization-register', [OrganizationController::class, 'organizationRegister']);
Route::post('check-user-name', [AuthController::class, 'checkUserName']);
Route::post('suggest-sub-domain', [OrganizationController::class, 'suggestSubDomain']);
Route::post('short-name', [OrganizationController::class, 'shortName']);
Route::get('organization-packages', [OrganizationController::class, 'organizationPackageList']);
//open route for website



Route::get('course-details', [MobileCourseController::class, 'getDetails']);
Route::get('course/{slug}', [MobileCourseController::class, 'getDetailsBySlug']);

Route::get('get-content-details', [MobileContentController::class, 'getWebContentDetails']);

Route::get('quiz-details/{quiz_id}', [CourseController::class, 'chapterQuizDetails']);

Route::get('category-wise-course-list', [MobileCategoryController::class, 'getList']);


Route::post('/submit-contact-form', [OrganizationController::class, 'submitContactForm'])->name('submit-contact-form');
// Route::post('/submit-contact-form', [OrganizationController::class, 'submitContactForm'])->name('submit-contact-form')->middleware('throttle:3,60');


Route::get('announcements', [AnnouncementController::class,  'getAnnouncementList']);


Route::get('menu-details', [MasterSettingsController::class, 'websiteMenuDetails']);
Route::get('course-list', [CourseController::class, 'coursesList']);
Route::get('content-list', [ContentController::class, 'contentsList']);
Route::get('content-outline-details/{content_subject_id}', [ContentController::class, 'ContentOutlineDetailsByID'])->middleware([AuthCheck::class]);


//
Route::get('landing-page-information', [OrganizationController::class, 'getLandinPageInformation']);
Route::get('landing-page-information-temp2', [OrganizationController::class, 'getLandinPageInformationTemp2']);





Route::middleware([CheckOrganization::class])->group(function () {

    Route::get('course-list-web', [MobileCourseController::class, 'getList']);
    Route::get('get-label-translations', [LabelTranslationController::class, 'getTranslations']);
    Route::get('testimonials', [TestimonialController::class, 'testimonialList']);

    Route::get('menu-list', [MasterSettingsController::class, 'websiteMenuList']);
    Route::get('auth-page-menu-list', [MasterSettingsController::class, 'websiteAuthMenuList']);

    Route::get('/page/{slug}', [MasterSettingsController::class, 'menuDetailsWebsite']);


    Route::middleware('auth:sanctum')->group(function () {


        Route::post('update-student-profile', [MobileStudentController::class, 'updateProfile']);

        // Route::get('script-details/{id}', [ContentController::class, 'scriptDetails'])->middleware([checkContent::class]);
        Route::get('script-details/{id}', [ContentController::class, 'scriptDetails']);
        // Route::get('video-details/{id}', [ContentController::class, 'videoDetails'])->middleware([checkContent::class]);
        Route::get('video-details/{id}', [ContentController::class, 'videoDetails']);
        Route::get('quiz-start-details/{id}', [CourseController::class, 'quizStartDetails']);
        Route::get('mentor-list', [MentorController::class, 'MentorList']);

        Route::post('image-update', [UserController::class, 'imageUpdate']);
        Route::post('purchase-course', [PaymentController::class, 'purchaseCourse']);
        Route::post('add-new-schedule', [CourseController::class, 'addClassSchedule']);
        Route::post('update-schedule', [CourseController::class, 'updateClassSchedule']);
        Route::post('delete-schedule', [CourseController::class, 'deleteClassSchedule']);
        Route::post('start-live-class', [CourseController::class, 'startLiveClass']);
        Route::post('end-live-class', [CourseController::class, 'endLiveClass']);
        Route::post('student-end-live-class', [CourseController::class, 'studentEndLiveClass']);
        Route::post('join-live-class', [CourseController::class, 'studentJoinClass']);
        Route::get('student-details-by-mapping-id/{mapping_id}', [StudentController::class, 'studentDetailsByMappingID']);
        Route::get('mentor-live-link', [MentorController::class, 'getZoomLink']);
        Route::post('update-link', [MentorController::class, 'updateZoomLink']);
        //Assignment Routes
        Route::get('class-list', [ContentController::class, 'classList']);
        Route::get('subject-list-by-class-id/{class_id}', [ContentController::class, 'subjectListByClassID']);
        Route::get('chapter-list-by-subject-id/{subject_id}', [ContentController::class, 'chapterListBySubjectID']);
        // Route::post('create-assignment', [AssignmentController::class, 'createAssignment']);
        Route::put('publish-assignment/{id}', [AssignmentController::class, 'publishAssignment']);
        // Route::get('assignment-list', [AssignmentController::class, 'assignmentList']);
        // Route::get('student-assignment-list', [AssignmentController::class, 'studentAssignmentList']);
        Route::get('resource-list-by-chapter-id/{chapter_id}', [ContentController::class, 'resourceListByChapterID']);
        Route::post('assignments', [AssignmentController::class, 'createAssignment']);
        Route::put('assignments/{id}', [AssignmentController::class, 'updateAssignment']);
        Route::delete('assignments/{id}', [AssignmentController::class, 'deleteAssignment']);
        Route::get('assignments/{id}', [AssignmentController::class, 'showAssignment']);
        //submit assignment
        Route::post('submit-assignment', [AssignmentController::class, 'storeSubmitAssignment']);
        Route::put('submit-assignment/{id}', [AssignmentController::class, 'updateSubmitAssignment']);
        Route::post('password-update', [AuthController::class, 'passwordUpdate']);

        Route::get('mark-video-completed', [ContentWatchLogController::class, 'markVideoCompleted']);
        Route::get('mark-script-completed', [ContentWatchLogController::class, 'markScriptCompleted']);

        Route::apiResource('organization-payment-typelist', PaymentTypeOrganizationController::class);
        Route::post('enroll-course', [PaymentController::class, 'enrollCourse']);

    });
});


Route::middleware('auth:sanctum')->group(function () {

    Route::post('process-payment', [OrganizationPaymentController::class, 'processPayment']);
    Route::post('make-stripe-payment', [OrganizationPaymentController::class, 'makeStripePayment']);
    

    // CyberSource existing routes
    Route::post('/cybersource/redirect', [CyberSourceRedirectController::class, 'getRedirectData']);
    Route::post('/cybersource/paybylink', [CyberSourceRedirectController::class, 'generateCyberSourceLink']);
    Route::post('/cybersource/paybyrest', [CyberSourceRedirectController::class, 'payByRESTApi']);

    // CyberSource Flex API routes for secure tokenization
    Route::post('/cybersource/capture-context', [CyberSourceRedirectController::class, 'getCaptureContext'])->name('cybersource.capture.context');
    Route::post('/cybersource/capture-context-alt', [CyberSourceRedirectController::class, 'getCaptureContextAlternative'])->name('cybersource.capture.context.alt');

    // CyberSource testing and diagnostic routes
    Route::get('/cybersource/test-credentials', [CyberSourceRedirectController::class, 'testCredentials'])->name('cybersource.test.credentials');
    Route::get('/cybersource/diagnostics', [CyberSourceRedirectController::class, 'runDiagnostics'])->name('cybersource.diagnostics');


    // Both Mentor and Student
    Route::post('logout-by-device', [AuthController::class, 'logoutByDevice']);
    Route::post('logout-all-device', [AuthController::class, 'logoutAllDevice']);
    Route::post('logout-exept-current-device', [AuthController::class, 'logoutExeptCurrentDevice']);
    Route::get('get-login-devices', [AuthController::class, 'getLoginDevices']);

    // Notification Routes
    Route::get('notifications', [NotificationController::class, 'getUserNotifications']);
    Route::get('organization-notifications', [NotificationController::class, 'getOrganizationNotifications']);
    Route::get('unread-notifications-count', [NotificationController::class, 'getUnreadCount']);
    Route::post('mark-notification-read/{id}', [NotificationController::class, 'markAsRead']);
    Route::post('mark-all-notifications-read', [NotificationController::class, 'markAllAsRead']);


    // Students
    Route::post('pay-due', [PaymentController::class, 'payDueByStudent']);
    Route::get('my-payments', [PaymentController::class, 'studentMyPayments']);
    Route::get('my-course-list', [MobileCourseController::class, 'getMyCourseList']);
    Route::get('certificates', [MobilePaymentController::class, 'getCertificates']);
    Route::get('certificates-details', [MobilePaymentController::class, 'getCertificateDetails']);
    Route::get('download-certificate', [MobilePaymentController::class, 'downloadCertificate']);
    Route::post('generate-certificates', [MobilePaymentController::class, 'generateCertificate']);
    Route::get('student-dashboard', [MobileCourseController::class, 'studentDashboardWeb']);
    Route::get('learning-activities', [MobileCourseController::class, 'learningActivities']);
    Route::get('learning-graph', [MobileCourseController::class, 'learningGraph']);


    Route::get('class-schedules', [ClassScheduleController::class, 'getLiveClassListForWeb']);
    Route::post('join-class', [ClassScheduleController::class, 'studentJoinClass']);


    Route::get('my-payment-list', [MobilePaymentController::class, 'myPaymentList']);

    Route::get('get-course-payment-details', [MobileCourseController::class, 'getPaymentDetails']);
    Route::get('student-assignment-list', [AssignmentController::class, 'studentAssignmentListMobile']);
    Route::get('assignment-details/{id}', [AssignmentController::class, 'assignmentDetails']);



    Route::get('mentor-dashboard', [MobileMentorController::class, 'webDashboard']);
    Route::get('mentor-activities', [MobileMentorController::class, 'activities']);


    Route::get('mentor-course-list-for-filter', [CourseController::class, 'courseListForFilterMentor']);


    Route::get('mentor-course-list', [MobileMentorController::class, 'myCourseList']);
    // Route::get('mentor-course-list', [MentorController::class, 'myCourseList']);
    Route::get('mentor-student-list', [CourseController::class, 'mentorStudentList']);
    Route::get('mentor-student-list-by-course/{course_id}', [CourseController::class, 'mentorStudentListByCourse']);
    Route::get('mentor-schedule-list/{mapping_id}', [CourseController::class, 'mentorClassScheduleList']);
    Route::get('mentor-completed-class-list', [CourseController::class, 'mentorCompletedClassList']);
    Route::get('mentor-ongoing-class-list', [CourseController::class, 'mentorOngoingClassList']);


    Route::get('student-join-history/{schedule_id}', [CourseController::class, 'studentClassJoinHistory']);
    Route::get('student-course-list', [StudentController::class, 'myCourseList']);
    Route::get('student-class-list', [CourseController::class, 'studentClassList']);
    Route::get('student-content-list', [StudentController::class, 'myContentList']);
    Route::get('student-purchase-list', [StudentController::class, 'myPurchaseList']);
    Route::get('student-quiz-participated-list', [CourseController::class, 'quizAnswerList']);
    Route::get('assignments', [AssignmentController::class, 'assignmentList']);
    // Route::get('student-assignment-list', [AssignmentController::class, 'studentAssignmentList']);
    Route::get('profile', [UserController::class, 'userProfile']);

    Route::get('student-quiz-result-details-by-id/{result_id}', [CourseController::class, 'quizAnswerDetails']);
    Route::get('student-subject-wise-result/{result_id}', [CourseController::class, 'quizSubjectWiseAnswerDetails']);
    // Route::get('quiz-details/{quiz_id}', [CourseController::class, 'chapterQuizDetails'])->middleware([checkContent::class]);

    Route::post('start-quiz', [CourseController::class, 'startQuiz']);
    Route::post('submit-quiz', [CourseController::class, 'submitQuizAnswer']);
    Route::post('submit-written-answer', [CourseController::class, 'submitWrittenAnswer']);



    Route::post('course-rating', [CourseRatingController::class, 'store']);



    Route::group(['prefix' => 'mentor'], function () {

        Route::post('update-profile', [MobileMentorController::class, 'updateProfile']);
        Route::get('student-list', [MobileCourseController::class, 'studentList']);

        Route::post('create-live-class-schedule', [ClassScheduleController::class, 'createLiveClassSchedule']);
        Route::post('delete-live-class-schedule', [ClassScheduleController::class, 'deleteLiveClassSchedule']);
        Route::post('update-live-class-schedule', [ClassScheduleController::class, 'updateLiveClassSchedule']);
        Route::get('live-class-list', [ClassScheduleController::class, 'mentorLiveClassList']);
        Route::get('live-class-details', [ClassScheduleController::class, 'mentorLiveClassDetails']);
        Route::post('start-live-class', [CourseController::class, 'startLiveClassWeb']);
        Route::post('end-live-class', [CourseController::class, 'endLiveClass']);
        Route::get('all-students-live-class', [ClassScheduleController::class, 'allStudentsLiveClass']);

        Route::post('create-assignment', [AssignmentController::class, 'createAssignment']);
        Route::get('assignment-list', [AssignmentController::class, 'assignmentList']);
        Route::get('all-students-assignment', [AssignmentController::class, 'allStudentsAssignment']);
        Route::put('update-assignment/{id}', [AssignmentController::class, 'updateAssignment']);
        Route::get('assignments-details', [AssignmentController::class, 'assignmentDetailsForMentorWeb']);
        Route::get('submission-details', [AssignmentController::class, 'submissionDetailsForMentor']);
        Route::put('publish-assignment', [AssignmentController::class, 'publishAssignment']);
        Route::post('mark-assignment', [AssignmentController::class, 'markAssignment']);
        Route::post('delete-assignment', [AssignmentController::class, 'deleteAssignment']);

        Route::get('batch-list', [BatchController::class, 'mentorBatchList']);

        Route::get('student-list-for-attendance', [AttendanceController::class, 'studentList']);
        Route::post('save-attendance', [AttendanceController::class, 'saveAttendance']);

    });



    // List discussions for a course
    Route::get('discussions', [DiscussionController::class, 'index']);

    // Create a new discussion
    Route::post('discussions', [DiscussionController::class, 'store']);

    // Get a specific discussion with comments
    Route::get('discussions/{id}', [DiscussionController::class, 'show']);

    // Add a comment to a discussion
    Route::post('comments', [DiscussionController::class, 'addComment']);

    // Like or unlike a discussion or comment
    Route::post('like', [DiscussionController::class, 'toggleLike']);

    // Report a discussion or comment
    Route::post('report', [DiscussionController::class, 'report']);



});


Route::post('coupon-check', [CouponController::class, 'couponCheck'])->name('coupon.check');

// CyberSource payment callback routes (outside auth middleware)
Route::post('/payment/success', [CyberSourceRedirectController::class, 'handleSuccess'])->name('cybersource.success');
Route::post('/payment/cancel', [CyberSourceRedirectController::class, 'handleCancel'])->name('cybersource.cancel');

    // // Payment Routes for bKash
    // Route::get('/bkash/payment', [App\Http\Controllers\BkashTokenizePaymentController::class,'index']);
    // Route::get('/bkash/create-payment', [App\Http\Controllers\BkashTokenizePaymentController::class,'createPayment'])->name('bkash-create-payment');
    // Route::get('/bkash/callback', [App\Http\Controllers\BkashTokenizePaymentController::class,'callBack'])->name('bkash-callBack');

    // //search payment
    // Route::get('/bkash/search/{trxID}', [App\Http\Controllers\BkashTokenizePaymentController::class,'searchTnx'])->name('bkash-serach');

    // //refund payment routes
    // Route::get('/bkash/refund', [App\Http\Controllers\BkashTokenizePaymentController::class,'refund'])->name('bkash-refund');
    // Route::get('/bkash/refund/status', [App\Http\Controllers\BkashTokenizePaymentController::class,'refundStatus'])->name('bkash-refund-status');




