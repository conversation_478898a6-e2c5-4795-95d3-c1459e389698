<?php

use App\Http\Controllers\AnnouncementController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\PaymentTypeController;
use App\Http\Controllers\PaymentTypeOrganizationController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ContentController;
use App\Http\Controllers\BatchController;
use App\Http\Controllers\CourseCategoryController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\AdminCourseController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MasterSettingsController;
use App\Http\Controllers\MentorController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\LabelTranslationController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\OfflineExamController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\PromotionalItemController;
use App\Http\Controllers\Mobile\ClassScheduleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CourseExternalLibraryController;
use App\Http\Controllers\TestimonialController;
use App\Http\Controllers\AdminDiscussionController;
use App\Http\Controllers\EbookController;
use App\Http\Controllers\CertificateTemplateController;
use App\Http\Controllers\PaymentGatewayController;
use App\Http\Controllers\OrganizationPaymentGatewayController;
use App\Http\Controllers\Admin\UserStoryController;
use App\Http\Controllers\Admin\UserStoryTestimonialController;
use App\Http\Controllers\Admin\CurrencyController;
use App\Http\Middleware\CheckOrganization;
use Illuminate\Support\Facades\Route;

    //new routes
    Route::get('generate-organization-code', [OrganizationController::class, 'generateOrganizationCode']);

    Route::post('upload-video', [DashboardController::class, 'uploadVideo']);
    Route::delete('delete-video/{id}', [DashboardController::class, 'deleteTempVideo']);

    Route::middleware(['auth:sanctum'])->group(function () {

    Route::get('get-profile', [UserController::class, 'getProfile']);
    Route::post('update-password', [UserController::class, 'updatePassword']);
    //Organization
    Route::apiResource('organizations', OrganizationController::class);
    Route::post('configure-organization', [OrganizationController::class, 'configure']);
    Route::apiResource('users', UserController::class);
    Route::post('change-password', [UserController::class, 'changePassword']);

    Route::get('enrollemnt-list', [PaymentController::class, 'enrollomentListAdmin']);

    Route::delete('delete-course/{id}', [CourseController::class, 'deleteCourse']);
    Route::post('duplicate-course', [CourseController::class, 'duplicateCourse']);
    Route::post('clone-course-content', [CourseController::class, 'cloneCourseContent']);

    Route::middleware([CheckOrganization::class])->group(function () {

        // Organization Currency Management Routes (New Implementation)
        Route::prefix('org-currencies')->group(function () {
            Route::get('/', [App\Http\Controllers\OrganizationCurrencyController::class, 'index'])->name('org.currencies.index');
            Route::post('/assign', [App\Http\Controllers\OrganizationCurrencyController::class, 'assignCurrency'])->name('org.currencies.assign');
            Route::put('/{id}', [App\Http\Controllers\OrganizationCurrencyController::class, 'updateAssignment'])->name('org.currencies.update');
            Route::delete('/{id}', [App\Http\Controllers\OrganizationCurrencyController::class, 'removeAssignment'])->name('org.currencies.remove');
            Route::get('/available', [App\Http\Controllers\OrganizationCurrencyController::class, 'getAvailableCurrencies'])->name('org.currencies.available');
        });

        Route::post('create-or-update-certificate-template', [CertificateTemplateController::class, 'createOrUpdate']);

        Route::get('dashboard', [DashboardController::class, 'dashboard']);

        // Dashboard APIs end
        Route::apiResource('ebooks', EbookController::class);
        Route::apiResource('promotional-items', PromotionalItemController::class);

        // Add LabelTranslation routes
        Route::apiResource('label-translations', LabelTranslationController::class);
        Route::get('get-label-translations', [LabelTranslationController::class, 'getTranslations']);

        Route::get('website-settings', [OrganizationController::class, 'getWebSettings']);
        Route::post('website-settings', [OrganizationController::class, 'storeWebSettings']);
        Route::put('website-settings/{id}', [OrganizationController::class, 'updateWebSettings']);
        Route::get('website-settings/{id}', [OrganizationController::class, 'getWebSettings']);
        Route::delete('website-settings/{id}', [OrganizationController::class, 'destroyWebSettings']);
        Route::get('script-details/{id}', [ContentController::class, 'scriptDetails']);
        Route::get('video-details/{id}', [ContentController::class, 'videoDetails']);
        Route::get('menu-list', [MasterSettingsController::class, 'adminMenuList']);
        Route::post('menu-save-or-update', [MasterSettingsController::class, 'saveOrUpdateMenu']);
        Route::post('add-items-to-menu', [MasterSettingsController::class, 'addItemsToMenu']);
        Route::put('update-items-of-menu', [MasterSettingsController::class, 'updateItemOfMenu']);
        Route::delete('menu-delete/{id}', [MasterSettingsController::class, 'menuDelete']);
        Route::get('menu/{id}', [MasterSettingsController::class, 'menuDetails']);
        Route::post('sort-menu', [MasterSettingsController::class, 'sortMenu']);
        Route::get('sub-menu', [CategoryController::class, 'subCategoryList']);
        Route::post('sub-menu', [CategoryController::class, 'subCategoryCreate']);
        Route::get('sub-menu/{id}', [CategoryController::class, 'subCategoryShow']);
        Route::put('sub-menu/{id}', [CategoryController::class, 'subCategoryUpdate']);
        Route::delete('sub-menu/{id}', [CategoryController::class, 'subCategoryDelete']);
        Route::get('sub-menu-by-menu/{id}', [CategoryController::class, 'subCategoryListByCategory']);
        Route::get('course-category', [CourseCategoryController::class, 'courseCategoryList']);
        Route::post('course-category', [CourseCategoryController::class, 'courseCategoryCreate']);
        Route::get('course-category/{id}', [CourseCategoryController::class, 'courseCategoryShow']);
        Route::put('course-category/{id}', [CourseCategoryController::class, 'courseCategoryUpdate']);
        Route::delete('course-category/{id}', [CourseCategoryController::class, 'courseCategoryDelete'])->middleware('verified');
        Route::post('mentor-create', [MentorController::class, 'mentorCreate']);
        Route::get('mentor-details/{mentor_id}', [MentorController::class, 'mentorDetailsByID']);
        Route::put('mentor-update/{id}', [MentorController::class, 'mentorUpdate']);
        Route::delete('mentor-delete/{id}', [MentorController::class, 'mentorDelete']);
        Route::post('student-create', [StudentController::class, 'studentCreate']);
        Route::put('student-update/{id}', [StudentController::class, 'studentUpdate']);
        Route::delete('student-delete/{id}', [StudentController::class, 'studentDelete']);
        Route::post('password-reset', [AuthController::class, 'passwordReset']);
        Route::post('tag-save-or-update', [MasterSettingsController::class, 'saveOrUpdateTags']);
        Route::post('tag-save-or-update-admin', [MasterSettingsController::class, 'tagsSaveOrUpdateAdmin']);
        Route::delete('delete-tag/{id}', [MasterSettingsController::class, 'tagsDelete']);
        Route::get('tag-list-admin', [MasterSettingsController::class, 'tagsListAdmin']);
        Route::get('subject-by-class-id/{class_id}', [ContentController::class, 'subjectListByClassID']);
        Route::get('chapter-by-subject-id/{subject_id}', [ContentController::class, 'chapterListBySubjectID']);
        Route::get('script-list-by-chapter-id/{chapter_id}', [ContentController::class, 'scriptListByChapterID']);
        Route::get('video-list-by-chapter-id/{chapter_id}', [ContentController::class, 'videoListByChapterID']);
        Route::get('quiz-list-by-chapter-id/{chapter_id}', [ContentController::class, 'quizListByChapterID']);
        Route::get('quiz-details-by-id/{id}', [ContentController::class, 'quizDetailsById']);
        Route::get('course-list-for-filter', [CourseController::class, 'courseListForFilter']);
        Route::get('mentor-list-for-filter', [MentorController::class, 'mentorListForFilter']);
        Route::get('student-list-for-filter-by-mentor', [StudentController::class, 'studentListForFilterByMentorId']);
        Route::get('class-list', [ContentController::class, 'classList']);
        Route::post('class-save-or-update', [ContentController::class, 'saveOrUpdateClass']);
        Route::get('student-Participant-list-by-course-id/{course_id}', [StudentController::class, 'courseParticipantList']);
        Route::get('student-list-for-batch-by-course-id/{course_id}', [StudentController::class, 'studentListForBatchByCourseId']);
        Route::get('course-list', [CourseController::class, 'courseList']);
        Route::get('course-details/{id}', [CourseController::class, 'courseDetails']);
        Route::post('course-save-or-update', [CourseController::class, 'saveOrUpdateCourse']);
        Route::delete('course-delete/{id}', [CourseController::class, 'courseDelete']);
        Route::get('course-type', [CourseController::class, 'courseTypeList']);
        Route::get('mentor-list', [CourseController::class, 'courseMentorList']);
        Route::get('student-list', [CourseController::class, 'courseStudentList']);
        Route::post('mentor-save-or-update', [MentorController::class, 'mentorSaveOrUpdate']);
        Route::get('all-mentor-list-admin', [MentorController::class, 'allMentorListAdmin']);
        Route::post('student-save-or-update', [StudentController::class, 'studentSaveOrUpdate']);
        Route::get('all-student-list-admin', [StudentController::class, 'allStudentAdmin']);
        Route::get('student-details-for-admin', [StudentController::class, 'studentDetailsForAdmin']);
        Route::get('course-list-for-mapping', [CourseController::class, 'courseListForStudentMapping']);
        Route::get('subject-list-by-class-id/{class_id}', [ContentController::class, 'subjectListByClassID']);
        Route::get('chapter-list-by-subject-id/{subject_id}', [ContentController::class, 'chapterListBySubjectID']);
        Route::get('assignments', [AssignmentController::class, 'assignmentListAdmin']);
        Route::post('create-assignment', [AssignmentController::class, 'createAssignmentAdmin']);


        Route::get('discussions', [AdminDiscussionController::class, 'index']);

        Route::get('discussions/{id}', [AdminDiscussionController::class, 'show']);

        Route::delete('discussions/{id}', [AdminDiscussionController::class, 'deleteDiscussion']);

        Route::delete('comments/{id}', [AdminDiscussionController::class, 'deleteComment']);

        Route::post('ban-user', [AdminDiscussionController::class, 'banUser']);
        Route::post('unban-user', [AdminDiscussionController::class, 'unbanUser']);
        Route::get('reports', [AdminDiscussionController::class, 'listReports']);

        Route::put('reports/{id}', [AdminDiscussionController::class, 'handleReport']);

        Route::get('banned-users', [AdminDiscussionController::class, 'listBannedUsers']);
        Route::put('discussions/{id}/toggle-pin', [AdminDiscussionController::class, 'togglePin']);
        Route::put('discussions/{id}/approve', [AdminDiscussionController::class, 'approveDiscussion']);

        Route::apiResource('organization-payment-gateways', OrganizationPaymentGatewayController::class);

        Route::apiResource('coupons', CouponController::class);
        Route::apiResource('announcements', AnnouncementController::class);


        Route::apiResource('offline-exams', OfflineExamController::class);
        Route::post('mark-offline-exam', [OfflineExamController::class, 'markOfflineExam']);

    });


    Route::apiResource('payment-gateways', PaymentGatewayController::class);

    Route::apiResource('course-external-libraries', CourseExternalLibraryController::class);
    Route::get('course-external-libraries-list', [CourseExternalLibraryController::class, 'getList']);
    Route::post('course-external-libraries/{id}/restore', [CourseExternalLibraryController::class, 'restore']);

    Route::post('collect-payment', [PaymentController::class, 'collectPayment']);
    Route::get('student-payments', [PaymentController::class, 'studentPayments']);
    Route::post('start-new-month', [PaymentController::class, 'startNewMonth']);
    Route::get('pending-payments', [PaymentController::class, 'getPendingPayments']);
    Route::get('all-payments', [PaymentController::class, 'getAllPayments']);
    Route::get('payments/{payment_id}', [PaymentController::class, 'paymentDetails']);

    Route::post('approve-pending-payment', [PaymentController::class, 'approvePendingEnrollment']);
    Route::post('enroll-student', [PaymentController::class, 'enrollStudentFromAdmin']);
    Route::post('check-enrollment-student', [PaymentController::class, 'checkEnrollmentStudent']);

    Route::get('core-subject-list', [ContentController::class, 'coreSubjectList']);
    Route::get('question-set-list', [ContentController::class, 'questionSetList']);
    Route::get('quiz-type-list', [ContentController::class, 'quizTypeList']);
    Route::get('subject-list', [ContentController::class, 'subjectList']);
    Route::post('subject-save-or-update', [ContentController::class, 'saveOrUpdateSubject']);
    Route::delete('subject-delete/{id}', [ContentController::class, 'deleteSubject']);
    Route::get('chapter-list', [ContentController::class, 'chapterList']);
    Route::post('chapter-save-or-update', [ContentController::class, 'saveOrUpdateChapter']);
    Route::get('video-chapter-list', [ContentController::class, 'videoChapterList']);
    Route::post('chapter-video-save-or-update', [ContentController::class, 'saveOrUpdateChapterVideo']);
    Route::delete('delete-chapter-video', [ContentController::class, 'deleteChapterVideo']);
    Route::get('chapter-script-list', [ContentController::class, 'scriptChapterList']);
    Route::post('chapter-script-save-or-update', [ContentController::class, 'saveOrUpdateScript']);
    Route::delete('delete-chapter-script', [ContentController::class, 'deleteChapterScript']);
    Route::post('chapter-quiz-save-or-update', [ContentController::class, 'saveOrUpdateQuiz']);
    Route::get('chapter-quiz-list', [ContentController::class, 'chapterQuizList']);
    Route::get('chapter-quiz-subject-list/{id}', [ContentController::class, 'chapterQuizSubjectList']);
    Route::post('quiz-subject-save-or-update', [ContentController::class, 'quizSubjectSaveOrUpdate']);
    Route::get('quiz-assign-subject-list/{id}', [ContentController::class, 'chapterQuizSubjectList']);
    Route::get('content-subject-list/{id}', [ContentController::class, 'contentSubjectList']);
    Route::post('content-subject-assign-save-or-update', [ContentController::class, 'contentSubjectAssignSaveOrUpdate']);
    Route::get('written-question-list/{id}', [ContentController::class, 'writtenQuestionList']);
    Route::post('written-question-save-or-update', [ContentController::class, 'saveOrUpdateWrittenQuestion']);
    Route::get('question-list-by-quiz/{id}', [ContentController::class, 'quizQuestionList']);
    Route::post('chapter-quiz-question-save-or-update', [ContentController::class, 'saveOrUpdateQuizQuestion']);
    Route::post('chapter-quiz-fill-in-blank-save-or-update', [ContentController::class, 'saveOrUpdateFillInBlankQuestion']);
    Route::post('chapter-quiz-true-false-save-or-update', [ContentController::class, 'saveOrUpdateTrueFalseQuestion']);
    Route::post('chapter-quiz-matching-save-or-update', [ContentController::class, 'saveOrUpdateMatchingQuestion']);
    Route::post('import-question', [ContentController::class, 'importQuestion']);
    Route::post('excel-question-upload', [ContentController::class, 'excelQuestionUpload']);
    Route::post('delete-question', [ContentController::class, 'deleteQuestion']);
    Route::post("delete-fill-in-blank-question", [ContentController::class, "deleteFillInBlankQuestion"]);
    Route::post("delete-true-false-question", [ContentController::class, "deleteTrueFalseQuestion"]);
    Route::post("delete-matching-question", [ContentController::class, "deleteMatchingQuestion"]);
    Route::post('website-page-save-or-update', [MasterSettingsController::class, 'websitePageSaveOrUpdate']);
    Route::get('website-page-list/{id}', [MasterSettingsController::class, 'websitePageList']);
    Route::post('course-outline-save-or-update', [CourseController::class, 'saveOrUpdateCourseOutline']);
    Route::get('course-outline-list/{id}', [CourseController::class, 'courseOutlineList']);
    Route::delete('delete-course-outline/{id}', [CourseController::class, 'courseOutlineDelete']);
    Route::get('content-list', [ContentController::class, 'contentList']);
    Route::post('content-save-or-update', [ContentController::class, 'saveOrUpdateContent']);
    Route::post('content-outline-save-or-update', [ContentController::class, 'saveOrUpdateContentOutline']);
    Route::get('content-outline-list/{id}', [ContentController::class, 'contentOutlineList']);
    Route::delete('delete-content-outline/{id}', [ContentController::class, 'contentOutlineDelete']);
    Route::post('faq-save-or-update', [CourseController::class, 'saveOrUpdateFaq']);
    Route::get('faq-list/{id}', [CourseController::class, 'faqList']);
    Route::delete('delete-faq/{id}', [CourseController::class, 'faqDelete']);
    Route::post('feature-save-or-update', [CourseController::class, 'saveOrUpdateFeature']);
    Route::post('learning-items-save-or-update', [CourseController::class, 'saveOrUpdateLearningItems']);
    Route::get('feature-list/{id}', [CourseController::class, 'featureList']);
    Route::delete('delete-feature/{id}', [CourseController::class, 'featureDelete']);
    Route::post('routine-save-or-update', [CourseController::class, 'saveOrUpdateRoutine']);
    Route::get('routine-list/{id}', [CourseController::class, 'routineList']);
    Route::delete('delete-routine/{id}', [CourseController::class, 'routineDelete']);
    Route::post('mentor-assign-save-or-update', [CourseController::class, 'saveOrUpdateAssignMentor']);
    Route::post('remove-mentor-from-course', [CourseController::class, 'removeMentorFromCourse']);
    Route::get('course-mentor-assign-list/{id}', [CourseController::class, 'mentorAssignList']);
    Route::delete('delete-mentor-assign/{id}', [CourseController::class, 'mentorAssignDelete']);
    Route::post('student-mapping-save-or-update', [CourseController::class, 'saveOrUpdateStudentMapping']);
    Route::delete('course-student-mapping-delete/{id}', [CourseController::class, 'courseStudentMappingDelete']);
    Route::get('student-mapping-list', [CourseController::class, 'studentMappingList']);
    Route::get('mentor-list-by-course', [CourseController::class, 'mentorListByCourse']);
    Route::get('course-payment-list-by-course-id/{course_id}', [StudentController::class, 'courseParticipantPaymentList']);
    Route::get('enrollment-list/{id}', [CourseController::class, 'enrollMentorList']);
    Route::post('course-free-enrollment', [CourseController::class, 'courseFreeEnrollment']);
    Route::get('completed-class-list', [CourseController::class, 'adminCompletedClassList']);
    Route::get('quiz-participated-list', [CourseController::class, 'quizAnswerListAdmin']);
    Route::get('student-quiz-result-details-by-id/{result_id}', [CourseController::class, 'quizAnswerDetails']);
    Route::get('student-subject-wise-result/{result_id}', [CourseController::class, 'quizSubjectWiseAnswerDetails']);


    Route::get('templates', [TemplateController::class, 'getTemplates']);
    Route::post('templates', [TemplateController::class, 'storeTemplate']);
    Route::put('templates/{id}', [TemplateController::class, 'updateTemplate']);
    Route::delete('templates/{id}', [TemplateController::class, 'deleteTemplate']);
    Route::get('templates/{id}', [TemplateController::class, 'showTemplate']);
    Route::get('template-items', [TemplateController::class, 'getTemplateItems']);
    Route::post('template-items', [TemplateController::class, 'storeTemplateItem']);
    Route::put('template-items/{id}', [TemplateController::class, 'updateTemplateItem']);
    Route::delete('template-items/{id}', [TemplateController::class, 'deleteTemplateItem']);
    Route::get('template-items/{id}', [TemplateController::class, 'showTemplateItem']);
    Route::get('quiz-details/{quiz_id}', [CourseController::class, 'chapterQuizDetails']);



    Route::post('import-course-material', [AdminCourseController::class, 'importCourseMaterial']);
    Route::post('import-mentor', [AdminCourseController::class, 'importMentor']);
    Route::post('import-student', [AdminCourseController::class, 'importStudent']);

    // Repository Pattern Here
    Route::apiResource('batches', BatchController::class);
    Route::post('batches-add-students', [BatchController::class, 'addStudents']);
    Route::post('batches-add-mentors', [BatchController::class, 'addMentors']);
    Route::get('batch-student-list', [BatchController::class, 'studentList']);

    Route::get('package-list', [PaymentController::class, 'packageList']);
    Route::get('package-details/{id}', [PaymentController::class, 'packageDetails']);
    Route::post('make-payment-organization', [PaymentController::class, 'makePaymentOrganization']);


    // Repository Pattern Stopped Here
    Route::get('student-list-for-attendance', [AttendanceController::class, 'studentList']);
    Route::post('save-attendance', [AttendanceController::class, 'saveAttendance']);

    Route::apiResource('payment-types', PaymentTypeController::class);
    Route::apiResource('organization-payment-types', PaymentTypeOrganizationController::class);

    Route::get('class-schedules', [ClassScheduleController::class, 'adminLiveClassList']);
    Route::get('class-schedules/{id}', [ClassScheduleController::class, 'adminLiveClassDetails']);
    Route::post('create-live-class-schedule', [ClassScheduleController::class, 'createLiveClassScheduleAdmin']);
    Route::delete('delete-live-class-schedule', [ClassScheduleController::class, 'deleteLiveClassScheduleAdmin']);
    Route::put('update-live-class-schedule', [ClassScheduleController::class, 'updateLiveClassScheduleAdmin']);



    Route::apiResource('testimonials', TestimonialController::class);
    Route::apiResource('user-stories', UserStoryController::class);

    // Route::get('user-stories', [UserStoryController::class, 'index']);

    // User Story Testimonials CRUD
    Route::apiResource('user-story-testimonials', UserStoryTestimonialController::class);
    Route::get('user-story-testimonials-user-stories', [UserStoryTestimonialController::class, 'getUserStories']);
    Route::post('user-story-testimonials-sort-order', [UserStoryTestimonialController::class, 'updateSortOrder']);
    Route::post('user-story-testimonials/{testimonial}/toggle-status', [UserStoryTestimonialController::class, 'toggleStatus']);

    // Organization Currency Management (for Organization Admins)
    Route::get('organization-currencies', [OrganizationController::class, 'getCurrencies']);
    Route::get('organization-currencies/available', [OrganizationController::class, 'getAvailableCurrencies']);
    Route::post('organization-currencies/assign', [OrganizationController::class, 'assignCurrency']);
    Route::put('organization-currencies/{currencyId}', [OrganizationController::class, 'updateCurrency']);
    Route::delete('organization-currencies/{currencyId}', [OrganizationController::class, 'removeCurrency']);
    Route::post('organization-currencies/{currencyId}/set-primary', [OrganizationController::class, 'setPrimaryCurrency']);


    //

    // Super Admin Routes
    Route::get('super-admin-dashboard', [DashboardController::class, 'superAdminDashboard']);
    Route::get('super-admin-organization-dashboard', [DashboardController::class, 'dashboard']);
    Route::get('super-admin-organization-list', [OrganizationController::class, 'organizationListForSuperAdmin']);
    Route::get('super-admin-organization-details/{id}', [OrganizationController::class, 'organizationDetailForSuperAdmin']);
    Route::post('super-admin-create-package', [PaymentController::class, 'createPackage']);
    Route::put('super-admin-update-package/{id}', [PaymentController::class, 'updatePackage']);
    Route::delete('super-admin-delete-package/{id}', [PaymentController::class, 'deletePackage']);
    Route::apiResource('payment-gateways', PaymentGatewayController::class);



    // Visitor Tracking Admin Routes (Authenticated)
    Route::get('/visitor-stats', [App\Http\Controllers\VisitorTrackingController::class, 'getVisitorStats'])->name('admin.visitor.stats');
    Route::get('/visitor-list', [App\Http\Controllers\VisitorTrackingController::class, 'getVisitorList'])->name('admin.visitor.list');

    // Super Admin Currency Management
    Route::apiResource('currencies', CurrencyController::class);


    // Notification routes
    Route::get('push-notification-test', [DashboardController::class, 'pushNotificationTest']);
    Route::get('notifications', [NotificationController::class, 'getUserNotifications']);
    Route::get('organization-notifications', [NotificationController::class, 'getOrganizationNotifications']);
    Route::post('mark-notification-read/{id}', [NotificationController::class, 'markAsRead']);
    Route::post('mark-all-notifications-read', [NotificationController::class, 'markAllAsRead']);
    Route::post('send-user-notification', [NotificationController::class, 'sendUserNotification']);
    Route::post('send-organization-notification', [NotificationController::class, 'sendOrganizationNotification']);

    // Legacy test route - can be removed in production
    Route::get('pusher-test', function () {
        $pusher = new \Pusher\Pusher(
            env('PUSHER_APP_KEY', '0fe88d769e216cb402cf'),
            env('PUSHER_APP_SECRET', '2cf91f652aef149cdc26'),
            env('PUSHER_APP_ID', '*******'),
            [
                'cluster' => env('PUSHER_APP_CLUSTER', 'ap2'),
                'useTLS' => true,
            ]
        );

        $data = ['message' => 'Test connection from raw Pusher call'];
        $pusher->trigger('edu-channel', 'message.sent', $data);

        return 'Pusher test sent';
    });
});
