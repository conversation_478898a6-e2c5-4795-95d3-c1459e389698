<?php

use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Api\CyberSourceRedirectController;
use App\Http\Controllers\Auth\AuthController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/login', function () {
    return view('welcome');
});

// Route::get('login', [AuthController::class, 'loginUser']);
Route::get('/email/verify/success', function () {
    return view('success-email');
})->name('verification.notice');

Route::get('/email/already-verified', function () {
    return view('already-verified');
})->name('already.verified');

Route::get('reset-password/{token}', [ResetPasswordController::class, 'showResetForm'])->name('password.reset.form');
Route::post('reset-password', [ResetPasswordController::class, 'reset'])->name('password.reset');


Route::post('/payment/success', [CyberSourceRedirectController::class, 'handleSuccess'])->name('cybersource.success');
Route::post('/payment/cancel', [CyberSourceRedirectController::class, 'handleCancel'])->name('cybersource.cancel');

// CyberSource testing routes
Route::get('/cybersource/test-credentials', [CyberSourceRedirectController::class, 'testCredentials'])->name('cybersource.test.credentials');
Route::post('/cybersource/test-payment', [CyberSourceRedirectController::class, 'payByRESTApi'])->name('cybersource.test.payment');
