<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberSource Secure Acceptance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: red;
            margin-top: 10px;
        }
        .success {
            color: green;
            margin-top: 10px;
        }
        .response-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .hidden-form {
            display: none;
        }
        .test-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CyberSource Secure Acceptance Test</h1>
        
        <div class="test-info">
            <h3>Test Information</h3>
            <p><strong>Environment:</strong> Test/Sandbox</p>
            <p><strong>Access Key:</strong> ad36521de6533073afc8626952c910b1</p>
            <p><strong>Profile ID:</strong> mehedi54321_1750580586</p>
            <p><strong>Backend URL:</strong> <span id="backend-url">http://localhost:8000</span></p>
        </div>

        <form id="payment-form">
            <div class="form-group">
                <label for="amount">Amount *</label>
                <input type="number" id="amount" name="amount" step="0.01" min="0.01" value="10.00" required>
            </div>

            <div class="form-group">
                <label for="currency">Currency *</label>
                <select id="currency" name="currency" required>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                </select>
            </div>

            <div class="form-group">
                <label for="reference_number">Reference Number</label>
                <input type="text" id="reference_number" name="reference_number" placeholder="Leave empty to auto-generate">
            </div>

            <div class="form-group">
                <label for="customer_name">Customer Name *</label>
                <input type="text" id="customer_name" name="customer_name" value="Test User" required>
            </div>

            <div class="form-group">
                <label for="customer_email">Email *</label>
                <input type="email" id="customer_email" name="customer_email" value="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="customer_phone">Phone</label>
                <input type="tel" id="customer_phone" name="customer_phone" value="+1234567890">
            </div>

            <h3>Billing Address (Optional)</h3>

            <div class="form-group">
                <label for="billing_line1">Address Line 1</label>
                <input type="text" id="billing_line1" name="billing_line1" value="123 Test Street">
            </div>

            <div class="form-group">
                <label for="billing_city">City</label>
                <input type="text" id="billing_city" name="billing_city" value="Test City">
            </div>

            <div class="form-group">
                <label for="billing_state">State</label>
                <input type="text" id="billing_state" name="billing_state" value="CA">
            </div>

            <div class="form-group">
                <label for="billing_postal_code">Postal Code</label>
                <input type="text" id="billing_postal_code" name="billing_postal_code" value="12345">
            </div>

            <div class="form-group">
                <label for="billing_country">Country</label>
                <select id="billing_country" name="billing_country">
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="GB">United Kingdom</option>
                </select>
            </div>

            <button type="submit" id="submit-btn">Generate Payment Form</button>
        </form>

        <div id="message"></div>
        <div id="response" class="response-container" style="display: none;"></div>

        <!-- Hidden form for CyberSource submission -->
        <form id="cybersource-form" class="hidden-form" method="POST"></form>
    </div>

    <script>
        const backendUrl = document.getElementById('backend-url').textContent;
        
        document.getElementById('payment-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submit-btn');
            const messageDiv = document.getElementById('message');
            const responseDiv = document.getElementById('response');
            
            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Processing...';
            messageDiv.innerHTML = '';
            responseDiv.style.display = 'none';
            
            try {
                // Collect form data
                const formData = new FormData(e.target);
                const paymentData = {
                    amount: formData.get('amount'),
                    currency: formData.get('currency'),
                    reference_number: formData.get('reference_number') || 'TEST-' + Date.now(),
                    customer_name: formData.get('customer_name'),
                    customer_email: formData.get('customer_email'),
                    customer_phone: formData.get('customer_phone'),
                    billing_address: {
                        line1: formData.get('billing_line1'),
                        city: formData.get('billing_city'),
                        state: formData.get('billing_state'),
                        postal_code: formData.get('billing_postal_code'),
                        country: formData.get('billing_country')
                    }
                };

                // Call Laravel backend
                const response = await fetch(`${backendUrl}/cybersource/secure-acceptance/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(paymentData)
                });

                const result = await response.json();

                if (result.success) {
                    messageDiv.innerHTML = '<div class="success">Payment form generated successfully! Redirecting to CyberSource...</div>';
                    
                    // Show response for debugging
                    responseDiv.innerHTML = '<h3>Generated Form Data:</h3><pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
                    responseDiv.style.display = 'block';
                    
                    // Submit to CyberSource after a short delay
                    setTimeout(() => {
                        submitToCyberSource(result.data);
                    }, 2000);
                    
                } else {
                    messageDiv.innerHTML = '<div class="error">Error: ' + (result.message || 'Unknown error') + '</div>';
                    if (result.errors) {
                        messageDiv.innerHTML += '<div class="error">Validation errors: ' + JSON.stringify(result.errors) + '</div>';
                    }
                }

            } catch (error) {
                console.error('Payment form generation error:', error);
                messageDiv.innerHTML = '<div class="error">Network error: ' + error.message + '</div>';
            } finally {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Generate Payment Form';
            }
        });

        function submitToCyberSource(paymentData) {
            const form = document.getElementById('cybersource-form');
            form.action = paymentData.action_url;
            form.innerHTML = ''; // Clear existing fields

            // Add all form fields as hidden inputs
            Object.entries(paymentData.form_fields).forEach(([key, value]) => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            });

            // Submit the form
            form.submit();
        }

        // Auto-generate reference number
        document.addEventListener('DOMContentLoaded', function() {
            const refInput = document.getElementById('reference_number');
            if (!refInput.value) {
                refInput.value = 'TEST-' + Date.now();
            }
        });
    </script>
</body>
</html>
