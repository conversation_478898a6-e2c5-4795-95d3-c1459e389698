<?php
/**
 * CyberSource Routes Test Script
 * 
 * This script helps test all the CyberSource routes that have been implemented.
 * Run this from the command line or use the individual cURL commands.
 */

// Configuration
$baseUrl = 'http://localhost:8000'; // Change this to your Laravel app URL
$authToken = 'your-auth-token-here'; // If you need authentication

echo "=== CyberSource Routes Test Script ===\n\n";

// Test routes array
$testRoutes = [
    [
        'name' => 'Test Credentials',
        'method' => 'GET',
        'url' => '/cybersource/test-credentials',
        'auth_required' => false,
        'description' => 'Tests CyberSource merchant credentials'
    ],
    [
        'name' => 'Run Diagnostics',
        'method' => 'GET', 
        'url' => '/cybersource/diagnostics',
        'auth_required' => false,
        'description' => 'Runs comprehensive diagnostic tests'
    ],
    [
        'name' => 'Generate Capture Context',
        'method' => 'POST',
        'url' => '/cybersource/capture-context',
        'auth_required' => true,
        'data' => ['target_origin' => 'http://localhost:3000'],
        'description' => 'Generates Flex API capture context for tokenization'
    ],
    [
        'name' => 'Generate Capture Context (Alternative)',
        'method' => 'POST',
        'url' => '/cybersource/capture-context-alt',
        'auth_required' => true,
        'data' => ['target_origin' => 'http://localhost:3000'],
        'description' => 'Alternative endpoint for capture context generation'
    ],
    [
        'name' => 'Test Payment (REST API)',
        'method' => 'POST',
        'url' => '/cybersource/paybyrest',
        'auth_required' => true,
        'description' => 'Tests payment processing via REST API'
    ],
    [
        'name' => 'Get Redirect Data',
        'method' => 'POST',
        'url' => '/cybersource/redirect',
        'auth_required' => true,
        'description' => 'Gets redirect data for hosted payment'
    ]
];

// Generate cURL commands
echo "=== cURL Commands for Testing ===\n\n";

foreach ($testRoutes as $route) {
    echo "## {$route['name']}\n";
    echo "Description: {$route['description']}\n";
    
    $curlCommand = "curl -X {$route['method']} \\\n";
    $curlCommand .= "  '{$baseUrl}{$route['url']}' \\\n";
    $curlCommand .= "  -H 'Content-Type: application/json' \\\n";
    $curlCommand .= "  -H 'Accept: application/json'";
    
    if ($route['auth_required']) {
        $curlCommand .= " \\\n  -H 'Authorization: Bearer {$authToken}'";
    }
    
    if (isset($route['data'])) {
        $curlCommand .= " \\\n  -d '" . json_encode($route['data']) . "'";
    }
    
    echo "```bash\n{$curlCommand}\n```\n\n";
}

// JavaScript examples for frontend integration
echo "=== JavaScript Frontend Integration Examples ===\n\n";

echo "## 1. Generate Capture Context\n";
echo "```javascript\n";
echo "async function getCaptureContext() {\n";
echo "    try {\n";
echo "        const response = await fetch('/cybersource/capture-context', {\n";
echo "            method: 'POST',\n";
echo "            headers: {\n";
echo "                'Content-Type': 'application/json',\n";
echo "                'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content'),\n";
echo "                'Authorization': 'Bearer ' + localStorage.getItem('auth_token')\n";
echo "            },\n";
echo "            body: JSON.stringify({\n";
echo "                target_origin: window.location.origin\n";
echo "            })\n";
echo "        });\n";
echo "        \n";
echo "        const data = await response.json();\n";
echo "        \n";
echo "        if (data.success) {\n";
echo "            console.log('Capture Context:', data.data.captureContext);\n";
echo "            return data.data.captureContext;\n";
echo "        } else {\n";
echo "            throw new Error(data.message);\n";
echo "        }\n";
echo "    } catch (error) {\n";
echo "        console.error('Error getting capture context:', error);\n";
echo "        throw error;\n";
echo "    }\n";
echo "}\n";
echo "```\n\n";

echo "## 2. Initialize CyberSource Flex\n";
echo "```javascript\n";
echo "async function initializeFlex() {\n";
echo "    try {\n";
echo "        const captureContext = await getCaptureContext();\n";
echo "        \n";
echo "        // Initialize CyberSource Flex\n";
echo "        const flex = new Flex(captureContext);\n";
echo "        \n";
echo "        // Create microform for card number\n";
echo "        const cardNumber = flex.microform({\n";
echo "            placeholder: 'Enter card number'\n";
echo "        });\n";
echo "        \n";
echo "        cardNumber.load('#card-number-container');\n";
echo "        \n";
echo "        // Handle form submission\n";
echo "        document.getElementById('payment-form').addEventListener('submit', async (e) => {\n";
echo "            e.preventDefault();\n";
echo "            \n";
echo "            try {\n";
echo "                const token = await cardNumber.createToken();\n";
echo "                \n";
echo "                if (token) {\n";
echo "                    console.log('Payment token:', token);\n";
echo "                    // Send token to your backend for payment processing\n";
echo "                    processPayment(token);\n";
echo "                }\n";
echo "            } catch (error) {\n";
echo "                console.error('Error creating token:', error);\n";
echo "            }\n";
echo "        });\n";
echo "        \n";
echo "    } catch (error) {\n";
echo "        console.error('Error initializing Flex:', error);\n";
echo "    }\n";
echo "}\n";
echo "```\n\n";

echo "=== Testing Checklist ===\n\n";
echo "1. ✅ Test credentials endpoint: GET /cybersource/test-credentials\n";
echo "2. ✅ Run diagnostics: GET /cybersource/diagnostics\n";
echo "3. ✅ Generate capture context: POST /cybersource/capture-context\n";
echo "4. ✅ Test alternative capture context: POST /cybersource/capture-context-alt\n";
echo "5. ✅ Test payment processing: POST /cybersource/paybyrest\n";
echo "6. ✅ Check Laravel logs for detailed information\n";
echo "7. ✅ Verify frontend integration with JavaScript examples\n\n";

echo "=== Next Steps ===\n\n";
echo "1. Start your Laravel development server: php artisan serve\n";
echo "2. Test the endpoints using the cURL commands above\n";
echo "3. Check the logs in storage/logs/laravel.log for detailed information\n";
echo "4. Integrate the JavaScript examples into your frontend\n";
echo "5. Move to production by updating credentials and URLs\n\n";

echo "=== Troubleshooting ===\n\n";
echo "- If you get 404 errors, make sure routes are properly registered\n";
echo "- If you get authentication errors, check your CyberSource credentials\n";
echo "- If you get CORS errors, configure your CORS settings\n";
echo "- Check Laravel logs for detailed error information\n\n";

echo "Script completed successfully!\n";
?>
