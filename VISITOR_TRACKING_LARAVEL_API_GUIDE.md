# Visitor Tracking Laravel API Implementation Guide

## Overview
Complete Laravel backend implementation for visitor tracking system. This tracks visitors on promotional pages: `/`, `/pricing`, and `/try-new-lms`.

## Database Schema
The migration has been created and run successfully:
- **Table**: `visitor_tracking`
- **Migration**: `database/migrations/2024_01_01_000000_create_visitor_tracking_table.php`

## API Endpoints

### 1. Track Visitor (Public)
**Endpoint**: `POST /website/track-visitor`
**Purpose**: Records a visitor's page visit with comprehensive tracking data
**Authentication**: None required (public endpoint)

**Request Body**:
```json
{
  "page_name": "home",
  "page_url": "https://yourdomain.com/",
  "page_path": "/",
  "session_id": "session_1703123456789_abc123def",
  "visitor_id": "visitor_1703123456789_xyz789ghi",
  "browser_name": "Chrome",
  "browser_version": "120.0.6099.109",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...",
  "device_type": "Desktop",
  "operating_system": "Windows",
  "screen_width": 1920,
  "screen_height": 1080,
  "viewport_width": 1536,
  "viewport_height": 864,
  "timezone": "America/New_York",
  "language": "en-US",
  "referrer": "https://google.com",
  "utm_source": "google",
  "utm_medium": "cpc",
  "utm_campaign": "winter_sale",
  "utm_term": "lms_software",
  "utm_content": "ad_variant_a",
  "visited_at": "2023-12-21T10:30:45.123Z",
  "additional_data": {
    "page_type": "landing",
    "section": "promotional"
  }
}
```

**Success Response** (200):
```json
{
  "success": true,
  "message": "Visitor tracked successfully",
  "data": {
    "tracking_id": 123
  }
}
```

**Validation Error** (422):
```json
{
  "success": false,
  "message": "Validation error",
  "errors": {
    "page_name": ["The page name field is required."]
  }
}
```

### 2. Get Visitor Statistics (Admin)
**Endpoint**: `GET /admin/visitor-stats`
**Purpose**: Retrieves aggregated visitor statistics for admin dashboard
**Authentication**: Required (Bearer token)

**Query Parameters**:
- `date_range` (optional): Number of days to look back (default: 7)

**Example**: `GET /admin/visitor-stats?date_range=30`

**Success Response**:
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_visitors": 1250,
      "total_page_views": 3420,
      "date_range": 7
    },
    "page_stats": [
      {
        "page_name": "home",
        "views": 1500,
        "unique_visitors": 800
      }
    ],
    "device_stats": [
      {
        "device_type": "Desktop",
        "count": 2100
      }
    ],
    "browser_stats": [
      {
        "browser_name": "Chrome",
        "count": 1800
      }
    ],
    "daily_stats": [
      {
        "date": "2023-12-21",
        "page_views": 450,
        "unique_visitors": 320
      }
    ],
    "utm_stats": [
      {
        "utm_source": "google",
        "count": 800
      }
    ]
  }
}
```

### 3. Get Visitor List (Admin)
**Endpoint**: `GET /admin/visitor-list`
**Purpose**: Retrieves detailed list of visitor records for admin review
**Authentication**: Required (Bearer token)

**Query Parameters**:
- `per_page` (optional): Number of records per page (default: 50)
- `page` (optional): Page number (default: 1)
- `page_name` (optional): Filter by specific page
- `date_range` (optional): Number of days to look back (default: 7)

**Example**: `GET /admin/visitor-list?per_page=25&page=1&page_name=home&date_range=14`

**Success Response**:
```json
{
  "success": true,
  "data": {
    "visitors": [
      {
        "id": 123,
        "page_name": "home",
        "page_url": "https://yourdomain.com/",
        "visitor_id": "visitor_1703123456789_xyz789ghi",
        "session_id": "session_1703123456789_abc123def",
        "browser_name": "Chrome",
        "browser_version": "120.0.6099.109",
        "device_type": "Desktop",
        "operating_system": "Windows",
        "timezone": "America/New_York",
        "language": "en-US",
        "referrer": "https://google.com",
        "utm_source": "google",
        "utm_medium": "cpc",
        "utm_campaign": "winter_sale",
        "visited_at": "2023-12-21T10:30:45.000000Z",
        "created_at": "2023-12-21T10:30:46.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 1250,
      "last_page": 50
    }
  }
}
```

### 4. Test Visitor Tracking
**Endpoint**: `GET /website/test-visitor-tracking`
**Purpose**: Tests the visitor tracking functionality with sample data
**Authentication**: None required

**Success Response**:
```json
{
  "success": true,
  "message": "Visitor tracking test completed successfully",
  "test_data": { /* sample data used for testing */ },
  "tracking_response": { /* response from tracking API */ },
  "instructions": [
    "1. The visitor tracking API is working correctly",
    "2. Test data has been inserted into the database",
    "3. You can now integrate this API with your frontend",
    "4. Use POST /website/track-visitor to track real visitors"
  ]
}
```

## Testing the APIs

### 1. Test the Visitor Tracking
```bash
# Test the tracking functionality
curl -X GET http://localhost:8000/website/test-visitor-tracking
```

### 2. Track a Visitor Manually
```bash
curl -X POST http://localhost:8000/website/track-visitor \
  -H "Content-Type: application/json" \
  -d '{
    "page_name": "home",
    "page_url": "http://localhost:8000/",
    "page_path": "/",
    "session_id": "session_test_123",
    "visitor_id": "visitor_test_123",
    "browser_name": "Chrome",
    "browser_version": "120.0",
    "device_type": "Desktop",
    "operating_system": "Windows",
    "visited_at": "2023-12-21T10:30:45.123Z"
  }'
```

### 3. Get Visitor Statistics (requires auth)
```bash
curl -X GET "http://localhost:8000/admin/visitor-stats?date_range=7" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"
```

### 4. Get Visitor List (requires auth)
```bash
curl -X GET "http://localhost:8000/admin/visitor-list?per_page=10&page=1" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"
```

## Frontend Integration Examples

### JavaScript/jQuery Example
```javascript
// Function to track visitor
function trackVisitor(pageName, additionalData = {}) {
    const visitorData = {
        page_name: pageName,
        page_url: window.location.href,
        page_path: window.location.pathname,
        session_id: getSessionId(),
        visitor_id: getVisitorId(),
        browser_name: getBrowserName(),
        browser_version: getBrowserVersion(),
        user_agent: navigator.userAgent,
        device_type: getDeviceType(),
        operating_system: getOperatingSystem(),
        screen_width: screen.width,
        screen_height: screen.height,
        viewport_width: window.innerWidth,
        viewport_height: window.innerHeight,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: navigator.language,
        referrer: document.referrer || null,
        utm_source: getUrlParameter('utm_source'),
        utm_medium: getUrlParameter('utm_medium'),
        utm_campaign: getUrlParameter('utm_campaign'),
        utm_term: getUrlParameter('utm_term'),
        utm_content: getUrlParameter('utm_content'),
        visited_at: new Date().toISOString(),
        additional_data: additionalData
    };

    fetch('/website/track-visitor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(visitorData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Visitor tracked successfully');
        } else {
            console.error('Failed to track visitor:', data.message);
        }
    })
    .catch(error => {
        console.error('Error tracking visitor:', error);
    });
}

// Helper functions
function getSessionId() {
    let sessionId = sessionStorage.getItem('visitor_session_id');
    if (!sessionId) {
        sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem('visitor_session_id', sessionId);
    }
    return sessionId;
}

function getVisitorId() {
    let visitorId = localStorage.getItem('visitor_id');
    if (!visitorId) {
        visitorId = 'visitor_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        localStorage.setItem('visitor_id', visitorId);
    }
    return visitorId;
}

function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// Usage examples
trackVisitor('home', { page_type: 'landing', section: 'promotional' });
trackVisitor('pricing', { page_type: 'pricing', section: 'promotional' });
trackVisitor('try-new-lms', { page_type: 'registration', section: 'trial' });
```

## Implementation Files Created

1. **Controller**: `app/Http/Controllers/VisitorTrackingController.php`
2. **Migration**: `database/migrations/2024_01_01_000000_create_visitor_tracking_table.php`
3. **Routes**: Added to `routes/website.php`

## Features Implemented

✅ **Comprehensive Data Collection**: Browser, device, screen, location, referrer, UTM parameters
✅ **Duplicate Prevention**: Prevents tracking same visitor multiple times per session
✅ **Flexible Additional Data**: JSON field for page-specific data
✅ **Admin Analytics**: Statistics and detailed visitor lists
✅ **Performance Optimized**: Proper indexing and efficient queries
✅ **Error Handling**: Comprehensive validation and error responses
✅ **Logging**: Detailed logging for debugging and monitoring
✅ **Test Endpoint**: Easy testing and validation

## Security & Privacy

- No personally identifiable information collected
- Uses browser-generated session/visitor IDs
- Public tracking endpoint (no auth required)
- Admin endpoints require authentication
- Input validation and sanitization
- Rate limiting recommended for production

## Next Steps

1. **Test the APIs** using the provided cURL commands
2. **Integrate with frontend** using the JavaScript examples
3. **Set up admin dashboard** to view visitor statistics
4. **Configure rate limiting** for production use
5. **Add GDPR compliance** if required for your region

The visitor tracking system is now fully implemented and ready for use!
