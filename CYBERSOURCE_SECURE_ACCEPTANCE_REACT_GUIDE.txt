================================================================================
                    CYBERSOURCE SECURE ACCEPTANCE - REACT INTEGRATION GUIDE
================================================================================

OVERVIEW
--------
This guide provides complete implementation for CyberSource Secure Acceptance 
payment integration with React frontend and <PERSON>vel backend.

CREDENTIALS USED
----------------
Access Key: ad36521de6533073afc8626952c910b1
Secret Key: 670419a54f7048ce93423c4862d1a7ec115a5605b2d64946b43d4614dcb55cf808e1e7927f664664a18b71e39ff9563569211ad4fcfe41e19d83c88b462312047d8fe89376fe4d9b916c331f0e407c9043d086149ce6459e81eb5baed4b0cdc6bdf361bd338947caa263c5403b8e8f6d629e414e26744e8a84d7922167a4363e
Profile ID: mehedi54321_1750580586

BACKEND ENDPOINTS
-----------------
1. Generate Payment Form: POST /cybersource/secure-acceptance/generate
2. Success Callback: POST /payment/success
3. Cancel Callback: POST /payment/cancel

================================================================================
                                REACT COMPONENTS
================================================================================

1. PAYMENT FORM COMPONENT (PaymentForm.jsx)
--------------------------------------------

import React, { useState } from 'react';
import axios from 'axios';

const PaymentForm = () => {
    const [formData, setFormData] = useState({
        amount: '',
        currency: 'USD',
        reference_number: '',
        customer_email: '',
        customer_name: '',
        customer_phone: '',
        billing_address: {
            line1: '',
            city: '',
            state: '',
            postal_code: '',
            country: 'US'
        }
    });
    
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        if (name.startsWith('billing_')) {
            const addressField = name.replace('billing_', '');
            setFormData(prev => ({
                ...prev,
                billing_address: {
                    ...prev.billing_address,
                    [addressField]: value
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const generateReferenceNumber = () => {
        return 'ORDER-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            // Generate reference number if not provided
            const paymentData = {
                ...formData,
                reference_number: formData.reference_number || generateReferenceNumber()
            };

            // Call Laravel backend to generate payment form
            const response = await axios.post('/cybersource/secure-acceptance/generate', paymentData, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.data.success) {
                // Create and submit the form to CyberSource
                submitToCyberSource(response.data.data);
            } else {
                setError(response.data.message || 'Failed to generate payment form');
            }
        } catch (err) {
            console.error('Payment form generation error:', err);
            setError(err.response?.data?.message || 'An error occurred while processing payment');
        } finally {
            setLoading(false);
        }
    };

    const submitToCyberSource = (paymentData) => {
        // Create a form element
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = paymentData.action_url;

        // Add all form fields as hidden inputs
        Object.entries(paymentData.form_fields).forEach(([key, value]) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        });

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    };

    return (
        <div className="payment-form-container">
            <h2>Secure Payment</h2>
            
            {error && (
                <div className="error-message" style={{color: 'red', marginBottom: '20px'}}>
                    {error}
                </div>
            )}

            <form onSubmit={handleSubmit} className="payment-form">
                <div className="form-group">
                    <label>Amount *</label>
                    <input
                        type="number"
                        name="amount"
                        value={formData.amount}
                        onChange={handleInputChange}
                        step="0.01"
                        min="0.01"
                        required
                        placeholder="0.00"
                    />
                </div>

                <div className="form-group">
                    <label>Currency *</label>
                    <select
                        name="currency"
                        value={formData.currency}
                        onChange={handleInputChange}
                        required
                    >
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                    </select>
                </div>

                <div className="form-group">
                    <label>Reference Number</label>
                    <input
                        type="text"
                        name="reference_number"
                        value={formData.reference_number}
                        onChange={handleInputChange}
                        placeholder="Leave empty to auto-generate"
                    />
                </div>

                <div className="form-group">
                    <label>Customer Name *</label>
                    <input
                        type="text"
                        name="customer_name"
                        value={formData.customer_name}
                        onChange={handleInputChange}
                        required
                        placeholder="John Doe"
                    />
                </div>

                <div className="form-group">
                    <label>Email *</label>
                    <input
                        type="email"
                        name="customer_email"
                        value={formData.customer_email}
                        onChange={handleInputChange}
                        required
                        placeholder="<EMAIL>"
                    />
                </div>

                <div className="form-group">
                    <label>Phone</label>
                    <input
                        type="tel"
                        name="customer_phone"
                        value={formData.customer_phone}
                        onChange={handleInputChange}
                        placeholder="+1234567890"
                    />
                </div>

                <h3>Billing Address</h3>
                
                <div className="form-group">
                    <label>Address Line 1</label>
                    <input
                        type="text"
                        name="billing_line1"
                        value={formData.billing_address.line1}
                        onChange={handleInputChange}
                        placeholder="123 Main Street"
                    />
                </div>

                <div className="form-group">
                    <label>City</label>
                    <input
                        type="text"
                        name="billing_city"
                        value={formData.billing_address.city}
                        onChange={handleInputChange}
                        placeholder="New York"
                    />
                </div>

                <div className="form-group">
                    <label>State</label>
                    <input
                        type="text"
                        name="billing_state"
                        value={formData.billing_address.state}
                        onChange={handleInputChange}
                        placeholder="NY"
                    />
                </div>

                <div className="form-group">
                    <label>Postal Code</label>
                    <input
                        type="text"
                        name="billing_postal_code"
                        value={formData.billing_address.postal_code}
                        onChange={handleInputChange}
                        placeholder="10001"
                    />
                </div>

                <div className="form-group">
                    <label>Country</label>
                    <select
                        name="billing_country"
                        value={formData.billing_address.country}
                        onChange={handleInputChange}
                    >
                        <option value="US">United States</option>
                        <option value="CA">Canada</option>
                        <option value="GB">United Kingdom</option>
                    </select>
                </div>

                <button 
                    type="submit" 
                    disabled={loading}
                    className="pay-button"
                    style={{
                        backgroundColor: '#007bff',
                        color: 'white',
                        padding: '12px 24px',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    {loading ? 'Processing...' : `Pay $${formData.amount}`}
                </button>
            </form>
        </div>
    );
};

export default PaymentForm;

================================================================================
                            PAYMENT RESULT COMPONENTS
================================================================================

2. PAYMENT SUCCESS COMPONENT (PaymentSuccess.jsx)
--------------------------------------------------

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const PaymentSuccess = () => {
    const [searchParams] = useSearchParams();
    const [paymentDetails, setPaymentDetails] = useState({});

    useEffect(() => {
        // Extract payment details from URL parameters
        const details = {
            transactionId: searchParams.get('transaction_id'),
            amount: searchParams.get('amount'),
            status: searchParams.get('status')
        };
        setPaymentDetails(details);
    }, [searchParams]);

    return (
        <div className="payment-success">
            <div className="success-icon">✅</div>
            <h1>Payment Successful!</h1>
            <p>Thank you for your payment. Your transaction has been processed successfully.</p>
            
            <div className="payment-details">
                <h3>Payment Details:</h3>
                <p><strong>Transaction ID:</strong> {paymentDetails.transactionId}</p>
                <p><strong>Amount:</strong> ${paymentDetails.amount}</p>
                <p><strong>Status:</strong> {paymentDetails.status}</p>
            </div>
            
            <button onClick={() => window.location.href = '/'}>
                Return to Home
            </button>
        </div>
    );
};

export default PaymentSuccess;

3. PAYMENT CANCEL COMPONENT (PaymentCancel.jsx)
------------------------------------------------

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const PaymentCancel = () => {
    const [searchParams] = useSearchParams();
    const [cancelDetails, setCancelDetails] = useState({});

    useEffect(() => {
        const details = {
            transactionUuid: searchParams.get('transaction_uuid'),
            reason: searchParams.get('reason')
        };
        setCancelDetails(details);
    }, [searchParams]);

    return (
        <div className="payment-cancel">
            <div className="cancel-icon">❌</div>
            <h1>Payment Cancelled</h1>
            <p>Your payment was cancelled. No charges have been made to your account.</p>
            
            {cancelDetails.reason && (
                <div className="cancel-reason">
                    <p><strong>Reason:</strong> {cancelDetails.reason}</p>
                </div>
            )}
            
            <div className="action-buttons">
                <button onClick={() => window.history.back()}>
                    Try Again
                </button>
                <button onClick={() => window.location.href = '/'}>
                    Return to Home
                </button>
            </div>
        </div>
    );
};

export default PaymentCancel;

4. PAYMENT ERROR COMPONENT (PaymentError.jsx)
----------------------------------------------

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const PaymentError = () => {
    const [searchParams] = useSearchParams();
    const [errorDetails, setErrorDetails] = useState({});

    useEffect(() => {
        const details = {
            reason: searchParams.get('reason'),
            message: searchParams.get('message')
        };
        setErrorDetails(details);
    }, [searchParams]);

    return (
        <div className="payment-error">
            <div className="error-icon">⚠️</div>
            <h1>Payment Error</h1>
            <p>An error occurred while processing your payment. Please try again.</p>
            
            {errorDetails.reason && (
                <div className="error-details">
                    <p><strong>Error:</strong> {errorDetails.reason}</p>
                    {errorDetails.message && (
                        <p><strong>Details:</strong> {errorDetails.message}</p>
                    )}
                </div>
            )}
            
            <div className="action-buttons">
                <button onClick={() => window.history.back()}>
                    Try Again
                </button>
                <button onClick={() => window.location.href = '/support'}>
                    Contact Support
                </button>
            </div>
        </div>
    );
};

export default PaymentError;

================================================================================
                                REACT ROUTER SETUP
================================================================================

5. APP ROUTER CONFIGURATION (App.jsx)
--------------------------------------

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PaymentForm from './components/PaymentForm';
import PaymentSuccess from './components/PaymentSuccess';
import PaymentCancel from './components/PaymentCancel';
import PaymentError from './components/PaymentError';

function App() {
    return (
        <Router>
            <div className="App">
                <Routes>
                    <Route path="/payment" element={<PaymentForm />} />
                    <Route path="/payment/success" element={<PaymentSuccess />} />
                    <Route path="/payment/cancel" element={<PaymentCancel />} />
                    <Route path="/payment/error" element={<PaymentError />} />
                    {/* Other routes */}
                </Routes>
            </div>
        </Router>
    );
}

export default App;

================================================================================
                                AXIOS CONFIGURATION
================================================================================

6. API CONFIGURATION (api.js)
------------------------------

import axios from 'axios';

// Create axios instance
const api = axios.create({
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
    timeout: 30000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // Handle unauthorized access
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

export default api;



TESTING STEPS
--------------

1. Start Laravel Backend:
   php artisan serve

2. Start React Frontend:
   npm start

3. Test Payment Flow:
   - Navigate to /payment
   - Fill in payment form
   - Submit form (redirects to CyberSource)
   - Complete payment on CyberSource hosted page
   - Verify redirect to success/cancel page

4. Test API Endpoint:
   curl -X POST http://localhost:8000/cybersource/secure-acceptance/generate \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "amount": "100.00",
       "currency": "USD",
       "reference_number": "TEST-001",
       "customer_email": "<EMAIL>",
       "customer_name": "John Doe"
     }'

CYBERSOURCE TEST CARDS
----------------------
- Visa: ****************
- Mastercard: ****************
- Amex: ***************
- Expiry: Any future date
- CVV: Any 3-4 digits

CALLBACK URLS
-------------
Success: http://localhost:3000/payment/success
Cancel: http://localhost:3000/payment/cancel
Error: http://localhost:3000/payment/error

================================================================================
                                SUPPORT INFORMATION
================================================================================

Laravel Backend Logs: storage/logs/laravel.log
CyberSource Documentation: https://developer.cybersource.com/
Test Environment: https://testsecureacceptance.cybersource.com/pay
Production Environment: https://secureacceptance.cybersource.com/pay

For issues, check Laravel logs and CyberSource transaction details in the 
Business Center portal.

================================================================================
