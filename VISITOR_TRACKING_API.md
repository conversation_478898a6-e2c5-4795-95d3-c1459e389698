# Visitor Tracking API

## Endpoints

### Track Visitor
**POST** `/api/website/track-visitor`

Records visitor data for promotional pages.

**Request Body:**
```json
{
  "page_name": "home",
  "page_url": "http://localhost:3000/",
  "page_path": "/",
  "session_id": "session_1750650997915_koqgeipxe",
  "visitor_id": "visitor_1750650997915_4c2tq88p3",
  "browser_name": "Chrome",
  "browser_version": "*********",
  "user_agent": "Mozilla/5.0...",
  "device_type": "Desktop",
  "operating_system": "Linux",
  "screen_width": 1920,
  "screen_height": 1080,
  "viewport_width": 1864,
  "viewport_height": 547,
  "timezone": "Asia/Dhaka",
  "language": "en-US",
  "referrer": "Direct",
  "utm_source": null,
  "utm_medium": null,
  "utm_campaign": null,
  "utm_term": null,
  "utm_content": null,
  "visited_at": "2025-06-23T04:11:31.418Z",
  "page_type": "landing",
  "section": "promotional"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Visitor tracked successfully",
  "data": {
    "tracking_id": 4
  }
}
```

### Get Visitor Statistics (Admin)
**GET** `/api/website/admin/visitor-stats?date_range=7`

Returns aggregated visitor statistics.

### Get Visitor List (Admin)
**GET** `/api/website/admin/visitor-list?per_page=50&page=1`

Returns paginated list of visitor records.

## Notes

- `referrer` can be "Direct" or a valid URL
- Extra fields like `page_type`, `section` are stored in `additional_data`
- Duplicate tracking is prevented per session/page
- Admin endpoints require authentication
