import { useEffect, useRef } from 'react';
import { useTrackVisitorMutation } from '@/store/api/master/visitorTrackingSlice';

/**
 * Custom hook for tracking visitor information
 * @param {string} pageName - Name of the page being tracked
 * @param {object} additionalData - Additional page-specific data
 * @param {object} options - Configuration options
 */
const useVisitorTracking = (pageName, additionalData = {}, options = {}) => {
  const [trackVisitor] = useTrackVisitorMutation();
  const hasTracked = useRef(false);
  const { 
    enabled = true, 
    trackOnMount = true,
    debounceMs = 1000 
  } = options;

  // Generate or retrieve session and visitor IDs
  const getSessionId = () => {
    let sessionId = sessionStorage.getItem('visitor_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('visitor_session_id', sessionId);
    }
    return sessionId;
  };

  const getVisitorId = () => {
    let visitorId = localStorage.getItem('visitor_id');
    if (!visitorId) {
      visitorId = `visitor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('visitor_id', visitorId);
    }
    return visitorId;
  };

  // Detect browser information
  const getBrowserInfo = () => {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';

    // Chrome
    if (userAgent.indexOf('Chrome') > -1) {
      browserName = 'Chrome';
      const match = userAgent.match(/Chrome\/([0-9.]+)/);
      if (match) browserVersion = match[1];
    }
    // Firefox
    else if (userAgent.indexOf('Firefox') > -1) {
      browserName = 'Firefox';
      const match = userAgent.match(/Firefox\/([0-9.]+)/);
      if (match) browserVersion = match[1];
    }
    // Safari
    else if (userAgent.indexOf('Safari') > -1) {
      browserName = 'Safari';
      const match = userAgent.match(/Version\/([0-9.]+)/);
      if (match) browserVersion = match[1];
    }
    // Edge
    else if (userAgent.indexOf('Edge') > -1) {
      browserName = 'Edge';
      const match = userAgent.match(/Edge\/([0-9.]+)/);
      if (match) browserVersion = match[1];
    }

    return { browserName, browserVersion };
  };

  // Detect device type
  const getDeviceType = () => {
    const userAgent = navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'Tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'Mobile';
    }
    return 'Desktop';
  };

  // Detect operating system
  const getOperatingSystem = () => {
    const userAgent = navigator.userAgent;
    if (userAgent.indexOf('Windows') > -1) return 'Windows';
    if (userAgent.indexOf('Mac') > -1) return 'macOS';
    if (userAgent.indexOf('Linux') > -1) return 'Linux';
    if (userAgent.indexOf('Android') > -1) return 'Android';
    if (userAgent.indexOf('iOS') > -1) return 'iOS';
    return 'Unknown';
  };

  // Extract UTM parameters from URL
  const getUtmParameters = () => {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      utm_source: urlParams.get('utm_source'),
      utm_medium: urlParams.get('utm_medium'),
      utm_campaign: urlParams.get('utm_campaign'),
      utm_term: urlParams.get('utm_term'),
      utm_content: urlParams.get('utm_content'),
    };
  };

  // Get referrer information
  const getReferrer = () => {
    return document.referrer || null;
  };

  // Collect comprehensive visitor data
  const collectVisitorData = () => {
    const { browserName, browserVersion } = getBrowserInfo();
    const utmParams = getUtmParameters();

    return {
      // Page Information
      page_name: pageName,
      page_url: window.location.href,
      page_path: window.location.pathname,

      // Session Information
      session_id: getSessionId(),
      visitor_id: getVisitorId(),

      // Browser Information
      browser_name: browserName,
      browser_version: browserVersion,
      user_agent: navigator.userAgent,

      // Device Information
      device_type: getDeviceType(),
      operating_system: getOperatingSystem(),

      // Screen Information
      screen_width: screen.width,
      screen_height: screen.height,
      viewport_width: window.innerWidth,
      viewport_height: window.innerHeight,

      // Location Information
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,

      // Referrer Information
      referrer: getReferrer(),
      ...utmParams,

      // Timestamp
      visited_at: new Date().toISOString(),

      // Additional Data
      additional_data: additionalData,
    };
  };

  // Track visitor function
  const track = async () => {
    if (!enabled || hasTracked.current) return;

    try {
      const visitorData = collectVisitorData();
      
      console.log('Tracking visitor:', {
        page: pageName,
        visitor_id: visitorData.visitor_id,
        session_id: visitorData.session_id
      });

      await trackVisitor(visitorData).unwrap();
      hasTracked.current = true;
      
      console.log('Visitor tracked successfully');
    } catch (error) {
      console.error('Failed to track visitor:', error);
    }
  };

  // Effect to track on mount
  useEffect(() => {
    if (trackOnMount && enabled) {
      const timer = setTimeout(track, debounceMs);
      return () => clearTimeout(timer);
    }
  }, [pageName, enabled, trackOnMount, debounceMs]);

  // Return the track function for manual tracking
  return { track, hasTracked: hasTracked.current };
};

export default useVisitorTracking;
