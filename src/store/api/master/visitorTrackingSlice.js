import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export const visitorTrackingApi = createApi({
  reducerPath: 'visitorTrackingApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/website/',
    prepareHeaders: (headers, { getState }) => {
      // Add auth token if available
      const token = getState()?.auth?.token || localStorage.getItem('auth_token');
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('Content-Type', 'application/json');
      headers.set('Accept', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['VisitorStats', 'VisitorList'],
  endpoints: (builder) => ({
    // Track visitor endpoint (public)
    trackVisitor: builder.mutation({
      query: (visitorData) => ({
        url: 'track-visitor',
        method: 'POST',
        body: visitorData,
      }),
      invalidatesTags: ['VisitorStats', 'VisitorList'],
    }),

    // Get visitor statistics (admin)
    getVisitorStats: builder.query({
      query: (params = {}) => ({
        url: '../admin/visitor-stats',
        method: 'GET',
        params,
      }),
      providesTags: ['VisitorStats'],
    }),

    // Get visitor list (admin)
    getVisitorList: builder.query({
      query: (params = {}) => ({
        url: '../admin/visitor-list',
        method: 'GET',
        params,
      }),
      providesTags: ['VisitorList'],
    }),
  }),
});

export const {
  useTrackVisitorMutation,
  useGetVisitorStatsQuery,
  useGetVisitorListQuery,
} = visitorTrackingApi;
