ORGANIZATION CURRENCY API ENDPOINTS
===================================

All endpoints are within CheckOrganization middleware - automatically filters by organization.

1. GET /api/admin/org-currencies
   Purpose: Get list of currencies assigned to current organization
   
   Query Parameters:
   - active_only: boolean (optional) - Filter only active currency assignments
   
   Request: No payload required
   
   Response:
   {
     "success": true,
     "data": [
       {
         "id": 1,
         "organization_id": 1,
         "currency_id": 1,
         "is_default": true,
         "is_active": true,
         "custom_exchange_rate": null,
         "effective_exchange_rate": "1.00000",
         "currency": {
           "id": 1,
           "name": "US Dollar",
           "symbol": "$",
           "code": "USD",
           "status": "active",
           "exchange_rate": "1.00000"
         },
         "formatted_display": "US Dollar ($) - Rate: 1.00000 - Active (Default)",
         "created_at": "2023-12-21T10:30:45.000000Z",
         "updated_at": "2023-12-21T10:30:45.000000Z"
       },
       {
         "id": 2,
         "organization_id": 1,
         "currency_id": 2,
         "is_default": false,
         "is_active": true,
         "custom_exchange_rate": "0.90000",
         "effective_exchange_rate": "0.90000",
         "currency": {
           "id": 2,
           "name": "Euro",
           "symbol": "€",
           "code": "EUR",
           "status": "active",
           "exchange_rate": "0.85000"
         },
         "formatted_display": "Euro (€) - Rate: 0.90000 - Active",
         "created_at": "2023-12-21T11:30:45.000000Z",
         "updated_at": "2023-12-21T11:30:45.000000Z"
       }
     ],
     "organization_id": 1
   }

2. POST /api/admin/org-currencies/assign
   Purpose: Assign a currency to the current organization
   
   Request Payload:
   {
     "currency_id": 3,
     "is_default": false,
     "is_active": true,
     "custom_exchange_rate": 0.75000
   }
   
   Response:
   {
     "success": true,
     "message": "Currency assigned to organization successfully",
     "data": {
       "id": 3,
       "organization_id": 1,
       "currency_id": 3,
       "is_default": false,
       "is_active": true,
       "custom_exchange_rate": "0.75000",
       "effective_exchange_rate": "0.75000",
       "currency": {
         "id": 3,
         "name": "British Pound",
         "symbol": "£",
         "code": "GBP",
         "status": "active",
         "exchange_rate": "0.73000"
       },
       "formatted_display": "British Pound (£) - Rate: 0.75000 - Active",
       "created_at": "2023-12-21T12:30:45.000000Z",
       "updated_at": "2023-12-21T12:30:45.000000Z"
     }
   }

3. PUT /api/admin/org-currencies/{id}
   Purpose: Update organization currency assignment

   Request Payload:
   {
     "is_default": true,
     "is_active": true,
     "custom_exchange_rate": 0.76000
   }
   
   Response:
   {
     "success": true,
     "message": "Currency assignment updated successfully",
     "data": {
       "id": 3,
       "organization_id": 1,
       "currency_id": 3,
       "is_default": true,
       "is_active": true,
       "custom_exchange_rate": "0.76000",
       "effective_exchange_rate": "0.76000",
       "currency": {
         "id": 3,
         "name": "British Pound",
         "symbol": "£",
         "code": "GBP",
         "status": "active",
         "exchange_rate": "0.73000"
       },
       "formatted_display": "British Pound (£) - Rate: 0.76000 - Active (Default)",
       "created_at": "2023-12-21T12:30:45.000000Z",
       "updated_at": "2023-12-21T13:15:30.000000Z"
     }
   }

4. DELETE /api/admin/org-currencies/{id}
   Purpose: Remove currency assignment from organization
   
   Request: No payload required (ID in URL)
   
   Response:
   {
     "success": true,
     "message": "Currency assignment removed successfully"
   }

5. GET /api/admin/org-currencies/available
   Purpose: Get currencies that can be assigned to the organization
   
   Request: No payload required
   
   Response:
   {
     "success": true,
     "data": [
       {
         "id": 4,
         "name": "Japanese Yen",
         "symbol": "¥",
         "code": "JPY",
         "status": "active",
         "is_default": false,
         "exchange_rate": "110.00000",
         "created_at": "2023-12-21T10:30:45.000000Z",
         "updated_at": "2023-12-21T10:30:45.000000Z",
         "deleted_at": null
       },
       {
         "id": 5,
         "name": "Canadian Dollar",
         "symbol": "C$",
         "code": "CAD",
         "status": "active",
         "is_default": false,
         "exchange_rate": "1.25000",
         "created_at": "2023-12-21T10:30:45.000000Z",
         "updated_at": "2023-12-21T10:30:45.000000Z",
         "deleted_at": null
       }
     ],
     "organization_id": 1
   }

FIELD DESCRIPTIONS:
==================
- id: Organization currency assignment ID (integer)
- organization_id: ID of the organization (integer)
- currency_id: ID of the currency (integer)
- is_default: Whether this is the default currency for the organization (boolean)
- is_active: Whether this currency assignment is active (boolean)
- custom_exchange_rate: Organization-specific exchange rate (decimal, nullable)
- effective_exchange_rate: The rate to use (custom_exchange_rate or currency.exchange_rate)
- currency: Full currency object with details
- formatted_display: Human-readable display string

VALIDATION RULES:
================
Assign Currency:
- currency_id: required, exists:currencies,id
- is_default: boolean
- is_active: boolean
- custom_exchange_rate: nullable, numeric, min:0

Update Assignment:
- is_default: boolean
- is_active: boolean
- custom_exchange_rate: nullable, numeric, min:0

BUSINESS RULES:
==============
1. Each organization can have multiple currencies
2. Only one currency can be set as default per organization
3. Cannot remove the default currency (must set another as default first)
4. Cannot assign the same currency twice to an organization
5. Only active currencies can be assigned
6. Custom exchange rate overrides the currency's default rate
7. CheckOrganization middleware ensures data isolation by organization

ERROR RESPONSES:
===============
Validation Error (422):
{
  "success": false,
  "message": "Validation error",
  "errors": {
    "currency_id": ["The currency id field is required."]
  }
}

Currency Already Assigned (409):
{
  "success": false,
  "message": "Currency is already assigned to this organization",
  "existing_assignment": { /* existing assignment data */ }
}

Assignment Not Found (404):
{
  "success": false,
  "message": "Currency assignment not found"
}

Cannot Remove Default (400):
{
  "success": false,
  "message": "Cannot remove default currency. Please set another currency as default first."
}

Server Error (500):
{
  "success": false,
  "message": "Failed to assign currency to organization",
  "error": "Error details"
}

AUTHENTICATION:
==============
All endpoints require authentication with Bearer token:
Authorization: Bearer {your_auth_token}

ORGANIZATION CONTEXT:
====================
- CheckOrganization middleware automatically determines organization from:
  1. URL host (subdomain-based tenancy)
  2. Authenticated user's organization_id
  3. Request parameter for admin users
- All operations are scoped to the current organization
- SystemAdmin/SuperAdmin can specify organization_id in request
