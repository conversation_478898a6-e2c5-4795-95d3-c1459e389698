<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visitor Tracking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .visitor-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Visitor Tracking System Test</h1>
        
        <div class="info">
            <h3>System Information</h3>
            <p><strong>Backend URL:</strong> <span id="backend-url">http://localhost:8000</span></p>
            <p><strong>Current Page:</strong> <span id="current-page">visitor_tracking_test.html</span></p>
            <p><strong>Session ID:</strong> <span id="session-id"></span></p>
            <p><strong>Visitor ID:</strong> <span id="visitor-id"></span></p>
        </div>

        <div class="visitor-info">
            <h3>Detected Visitor Information</h3>
            <div id="visitor-details"></div>
        </div>

        <div class="test-section">
            <h3>1. Test Visitor Tracking API</h3>
            <p>This will test the visitor tracking functionality with current browser data.</p>
            <button onclick="testVisitorTracking()">Test Visitor Tracking</button>
            <div id="tracking-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Track Different Pages</h3>
            <p>Test tracking for different promotional pages:</p>
            <button onclick="trackPage('home', {page_type: 'landing', section: 'promotional'})">Track Home Page</button>
            <button onclick="trackPage('pricing', {page_type: 'pricing', section: 'promotional'})">Track Pricing Page</button>
            <button onclick="trackPage('try-new-lms', {page_type: 'registration', section: 'trial'})">Track Try New LMS</button>
            <div id="page-tracking-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Test API Endpoints</h3>
            <p>Test the visitor tracking API endpoints directly:</p>
            <button onclick="testTrackingEndpoint()">Test Tracking Endpoint</button>
            <button onclick="testStatsEndpoint()">Test Stats Endpoint (Requires Auth)</button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const backendUrl = 'http://localhost:8000';
        
        // Visitor tracking utility functions
        function getSessionId() {
            let sessionId = sessionStorage.getItem('visitor_session_id');
            if (!sessionId) {
                sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                sessionStorage.setItem('visitor_session_id', sessionId);
            }
            return sessionId;
        }

        function getVisitorId() {
            let visitorId = localStorage.getItem('visitor_id');
            if (!visitorId) {
                visitorId = 'visitor_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('visitor_id', visitorId);
            }
            return visitorId;
        }

        function getBrowserInfo() {
            const userAgent = navigator.userAgent;
            let browserName = 'Unknown';
            let browserVersion = 'Unknown';

            if (userAgent.indexOf('Chrome') > -1) {
                browserName = 'Chrome';
                const match = userAgent.match(/Chrome\/([0-9.]+)/);
                if (match) browserVersion = match[1];
            } else if (userAgent.indexOf('Firefox') > -1) {
                browserName = 'Firefox';
                const match = userAgent.match(/Firefox\/([0-9.]+)/);
                if (match) browserVersion = match[1];
            } else if (userAgent.indexOf('Safari') > -1) {
                browserName = 'Safari';
                const match = userAgent.match(/Version\/([0-9.]+)/);
                if (match) browserVersion = match[1];
            }

            return { browserName, browserVersion };
        }

        function getDeviceType() {
            const userAgent = navigator.userAgent;
            if (/tablet|ipad|playbook|silk/i.test(userAgent)) return 'Tablet';
            if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) return 'Mobile';
            return 'Desktop';
        }

        function getOperatingSystem() {
            const userAgent = navigator.userAgent;
            if (userAgent.indexOf('Windows') > -1) return 'Windows';
            if (userAgent.indexOf('Mac') > -1) return 'macOS';
            if (userAgent.indexOf('Linux') > -1) return 'Linux';
            if (userAgent.indexOf('Android') > -1) return 'Android';
            if (userAgent.indexOf('iOS') > -1) return 'iOS';
            return 'Unknown';
        }

        function getUtmParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                utm_source: urlParams.get('utm_source'),
                utm_medium: urlParams.get('utm_medium'),
                utm_campaign: urlParams.get('utm_campaign'),
                utm_term: urlParams.get('utm_term'),
                utm_content: urlParams.get('utm_content'),
            };
        }

        function collectVisitorData(pageName, additionalData = {}) {
            const { browserName, browserVersion } = getBrowserInfo();
            const utmParams = getUtmParameters();

            return {
                page_name: pageName,
                page_url: window.location.href,
                page_path: window.location.pathname,
                session_id: getSessionId(),
                visitor_id: getVisitorId(),
                browser_name: browserName,
                browser_version: browserVersion,
                user_agent: navigator.userAgent,
                device_type: getDeviceType(),
                operating_system: getOperatingSystem(),
                screen_width: screen.width,
                screen_height: screen.height,
                viewport_width: window.innerWidth,
                viewport_height: window.innerHeight,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                language: navigator.language,
                referrer: document.referrer || null,
                ...utmParams,
                visited_at: new Date().toISOString(),
                additional_data: additionalData
            };
        }

        // Test functions
        async function testVisitorTracking() {
            const resultDiv = document.getElementById('tracking-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing visitor tracking...';
            resultDiv.className = 'result';

            try {
                const visitorData = collectVisitorData('test_page', { test_mode: true });
                
                const response = await fetch(`${backendUrl}/api/website/website/track-visitor`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(visitorData)
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `Success! Tracking ID: ${result.data.tracking_id}\n\nSent Data:\n${JSON.stringify(visitorData, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Error: ${result.message}\n\nResponse:\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Network Error: ${error.message}`;
            }
        }

        async function trackPage(pageName, additionalData) {
            const resultDiv = document.getElementById('page-tracking-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = `Tracking ${pageName} page...`;
            resultDiv.className = 'result';

            try {
                const visitorData = collectVisitorData(pageName, additionalData);
                
                const response = await fetch(`${backendUrl}/api/website/website/track-visitor`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(visitorData)
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `Successfully tracked ${pageName} page!\nTracking ID: ${result.data.tracking_id}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Error tracking ${pageName}: ${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Network Error: ${error.message}`;
            }
        }

        async function testTrackingEndpoint() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing tracking endpoint...';
            resultDiv.className = 'result';

            try {
                const response = await fetch(`${backendUrl}/api/website/website/test-visitor-tracking`);
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `API Test Successful!\n\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `API Test Failed:\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Network Error: ${error.message}`;
            }
        }

        async function testStatsEndpoint() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing stats endpoint (requires authentication)...';
            resultDiv.className = 'result';

            try {
                const response = await fetch(`${backendUrl}/api/website/admin/visitor-stats`);
                const result = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `Stats Endpoint Response:\n${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Network Error: ${error.message}`;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Display session and visitor IDs
            document.getElementById('session-id').textContent = getSessionId();
            document.getElementById('visitor-id').textContent = getVisitorId();
            
            // Display visitor details
            const { browserName, browserVersion } = getBrowserInfo();
            const visitorDetails = document.getElementById('visitor-details');
            visitorDetails.innerHTML = `
                <p><strong>Browser:</strong> ${browserName} ${browserVersion}</p>
                <p><strong>Device:</strong> ${getDeviceType()}</p>
                <p><strong>OS:</strong> ${getOperatingSystem()}</p>
                <p><strong>Screen:</strong> ${screen.width}x${screen.height}</p>
                <p><strong>Viewport:</strong> ${window.innerWidth}x${window.innerHeight}</p>
                <p><strong>Timezone:</strong> ${Intl.DateTimeFormat().resolvedOptions().timeZone}</p>
                <p><strong>Language:</strong> ${navigator.language}</p>
            `;
        });
    </script>
</body>
</html>
