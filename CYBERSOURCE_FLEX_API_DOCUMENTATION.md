# CyberSource Flex API - Laravel Implementation

## Overview
This document describes the Laravel implementation of CyberSource Flex API for secure card tokenization. The Flex API allows you to securely collect payment information without sensitive data touching your servers.

## Endpoints

### 1. Generate Capture Context (Primary)
**Endpoint**: `POST /cybersource/capture-context`

**Description**: Generates a capture context (session) for CyberSource Flex tokenization.

**Request Body** (Optional):
```json
{
    "target_origin": "https://your-website.com"
}
```

**Parameters**:
- `target_origin` (optional): The domain where the Flex form will be hosted. Defaults to `http://localhost:3000`

**Success Response** (200):
```json
{
    "success": true,
    "message": "Capture context generated successfully",
    "data": {
        "captureContext": "session_id_here",
        "flexSession": "session_id_here",
        "sessionId": "session_id_here",
        "fullResponse": {
            // Complete CyberSource response
        }
    }
}
```

**Error Response** (4xx/5xx):
```json
{
    "success": false,
    "message": "Failed to generate capture context",
    "error": "Error description",
    "status_code": 400,
    "details": {
        // CyberSource error details
    }
}
```

### 2. Generate Capture Context (Alternative)
**Endpoint**: `POST /cybersource/capture-context-alt`

**Description**: Alternative endpoint using different CyberSource Flex API structure. Use this if the primary endpoint doesn't work.

**Request/Response**: Same as primary endpoint

## Usage Examples

### JavaScript Frontend Integration
```javascript
// 1. Get capture context from your Laravel backend
async function getCaptureContext() {
    try {
        const response = await fetch('/cybersource/capture-context', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                target_origin: window.location.origin
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            return data.data.captureContext;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('Error getting capture context:', error);
        throw error;
    }
}

// 2. Use the capture context with CyberSource Flex
async function initializeFlex() {
    try {
        const captureContext = await getCaptureContext();
        
        // Initialize CyberSource Flex with the capture context
        const flex = new Flex(captureContext);
        
        // Create microform for card number
        const cardNumber = flex.microform({
            placeholder: 'Enter card number'
        });
        
        cardNumber.load('#card-number-container');
        
        // Handle form submission
        document.getElementById('payment-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Create token
            const token = await cardNumber.createToken();
            
            if (token) {
                // Send token to your backend for payment processing
                processPayment(token);
            }
        });
        
    } catch (error) {
        console.error('Error initializing Flex:', error);
    }
}
```

### cURL Examples

**Generate Capture Context**:
```bash
curl -X POST http://your-domain.com/cybersource/capture-context \
  -H "Content-Type: application/json" \
  -d '{
    "target_origin": "https://your-website.com"
  }'
```

**Alternative Endpoint**:
```bash
curl -X POST http://your-domain.com/cybersource/capture-context-alt \
  -H "Content-Type: application/json" \
  -d '{
    "target_origin": "https://your-website.com"
  }'
```

## Configuration

### Current Credentials
The implementation uses the following CyberSource credentials:
- **Merchant ID**: `mehedi54321_1750580586`
- **API Key**: `c95a54f4-12bd-4703-a7e0-a1b64c0665fb`
- **Environment**: Test (`https://apitest.cybersource.com`)

### Endpoints Used
- **Primary**: `https://apitest.cybersource.com/flex/v2/sessions`
- **Alternative**: `https://testflex.cybersource.com/flex/v2/sessions`

## Security Considerations

1. **HTTPS Only**: Always use HTTPS in production
2. **Target Origin**: Specify exact domain in `target_origin` for production
3. **CSRF Protection**: Include CSRF tokens in frontend requests
4. **Environment Variables**: Move credentials to environment variables in production

## Troubleshooting

### Common Issues

1. **401 Unauthorized**:
   - Verify API credentials are correct
   - Check if Flex API is enabled for your merchant account

2. **403 Forbidden**:
   - Ensure Flex services are enabled in CyberSource Business Center
   - Verify account permissions

3. **404 Not Found**:
   - Try the alternative endpoint
   - Check if your account supports Flex API

### Logging
All requests and responses are logged in Laravel logs for debugging:
- Check `storage/logs/laravel.log` for detailed information
- Look for entries prefixed with "CyberSource Flex"

## Next Steps

1. **Test the endpoints** using the provided cURL examples
2. **Integrate with frontend** using the JavaScript examples
3. **Move to production** by updating credentials and URLs
4. **Implement error handling** in your frontend application

## Related Documentation
- [CyberSource Flex API Documentation](https://developer.cybersource.com/docs/cybs/en-us/digital-accept-flex/developer/all/rest/digital-accept-flex.html)
- [CyberSource Developer Center](https://developer.cybersource.com/)
